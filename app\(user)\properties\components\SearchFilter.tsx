import {
  MapPin,
  DollarSign,
  Ruler,
  Building,
  Home,
  ChevronDown,
  Building2,
  LandPlot,
  Store,
  KeyRound,
  BedDouble,
  CircleCheck,
  WalletCards,
  Loader2,
  XIcon,
  SlidersHorizontal,
  Warehouse,
  Factory,
  Hotel,
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuGroup,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from '@/components/ui/sheet';
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
  DrawerHeader,
  DrawerTitle,
  DrawerFooter,
} from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import Image from 'next/image';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import * as RadioGroup from '@radix-ui/react-radio-group';
import { AreaChart, Area, ResponsiveContainer, XAxis, YAxis, Tooltip } from 'recharts';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import { memo, useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useIsMobile } from '@/hooks/useMobile';
import SearchAutocompleteLocation from '@/components/searchAutocompleteLocation';
import {
  TransactionType,
  PropertyType,
  PropertyDetailFilters,
  Amenity as AmenityType,
} from '@/lib/api/services/fetchProperty';
import { useSearchStore, CENTRAL_CITIES } from '../store/useSearchStore';
import SaveSearchButton from './SaveSearchButton';

// --- Local Constants for UI ---
const propertyTypes = [
  { id: PropertyType.APARTMENT, label: 'Chung cư', icon: Building2 },
  { id: PropertyType.VILLA, label: 'Biệt thự', icon: Home },
  { id: PropertyType.LAND_PLOT, label: 'Đất', icon: LandPlot },
  { id: PropertyType.SHOP_HOUSE, label: 'Nhà phố', icon: Store },
  { id: PropertyType.OFFICE, label: 'Văn phòng', icon: Building },
  { id: PropertyType.WAREHOUSE, label: 'Kho', icon: Warehouse },
  { id: PropertyType.FACTORY, label: 'Nhà máy', icon: Factory },
  { id: PropertyType.INDUSTRIAL, label: 'Công nghiệp', icon: Factory },
  { id: PropertyType.HOTEL, label: 'Khách sạn', icon: Hotel },
  { id: PropertyType.SOCIAL_HOUSING, label: 'Nhà ở xã hội', icon: Home },
  { id: PropertyType.NEW_URBAN_AREA, label: 'Khu đô thị mới', icon: Building },
  { id: PropertyType.ECO_RESORT, label: 'Khu resort', icon: Building },
  { id: PropertyType.OTHER, label: 'Khác', icon: Building },
];

const transactionTypesData = [
  { id: 'both', label: 'Tất cả', icon: WalletCards },
  { id: TransactionType.FOR_SALE, label: 'Bán', icon: DollarSign },
  { id: TransactionType.FOR_RENT, label: 'Cho thuê', icon: KeyRound },
];

const bedOptions = [
  { id: 'any', label: 'Bất kỳ' },
  { id: '1+', label: '1+' },
  { id: '2+', label: '2+' },
  { id: '3+', label: '3+' },
  { id: '4+', label: '4+' },
  { id: '5+', label: '5+' },
];

const exactBedOptions = [
  { id: 'studio', label: 'Studio' },
  { id: '1', label: '1' },
  { id: '2', label: '2' },
  { id: '3', label: '3' },
  { id: '4', label: '4' },
  { id: '5', label: '5' },
];

const bathOptions = [
  { id: 'any', label: 'Bất kỳ' },
  { id: '1+', label: '1+' },
  { id: '2+', label: '2+' },
  { id: '3+', label: '3+' },
  { id: '4+', label: '4+' },
  { id: '5+', label: '5+' },
];

const livingRoomOptions = [
  { id: 'any', label: 'Bất kỳ' },
  { id: '1+', label: '1+' },
  { id: '2+', label: '2+' },
  { id: '3+', label: '3+' },
];

const kitchenOptions = [
  { id: 'any', label: 'Bất kỳ' },
  { id: '1+', label: '1+' },
  { id: '2+', label: '2+' },
  { id: '3+', label: '3+' },
];

const amenityOptions: { id: keyof AmenityType; label: string }[] = [
  { id: 'parking', label: 'Chỗ đậu xe' },
  { id: 'elevator', label: 'Thang máy' },
  { id: 'swimmingPool', label: 'Hồ bơi' },
  { id: 'gym', label: 'Phòng gym' },
  { id: 'securitySystem', label: 'An ninh 24/7' },
  { id: 'airConditioning', label: 'Máy lạnh' },
  { id: 'balcony', label: 'Ban công' },
  { id: 'garden', label: 'Sân vườn' },
  { id: 'playground', label: 'Sân chơi trẻ em' },
  { id: 'backupGenerator', label: 'Máy phát điện' },
];

const detailOptions: { id: PropertyDetailFilters; label: string }[] = [
  { id: PropertyDetailFilters.hasBasement, label: 'Có tầng hầm' },
  { id: PropertyDetailFilters.furnished, label: 'Có nội thất' },
];

const priceDistributionSale = [
  { price: '0-1', count: 120 },
  { price: '1-2', count: 250 },
  { price: '2-3', count: 180 },
  { price: '3-4', count: 90 },
  { price: '4-5', count: 60 },
  { price: '5-6', count: 40 },
  { price: '6-7', count: 30 },
  { price: '7-8', count: 20 },
  { price: '8-9', count: 15 },
  { price: '9-10', count: 10 },
];
const priceDistributionRent = [
  { price: '0-5', count: 150 },
  { price: '5-10', count: 280 },
  { price: '10-15', count: 320 },
  { price: '15-20', count: 250 },
  { price: '20-25', count: 180 },
  { price: '25-30', count: 120 },
  { price: '30-40', count: 80 },
  { price: '40-50', count: 50 },
  { price: '50-70', count: 30 },
  { price: '70+', count: 20 },
];

const quickPriceRangesSale = [
  { label: 'Dưới 1 tỷ', value: [0, 1000000000] },
  { label: '1-2 tỷ', value: [1000000000, 2000000000] },
  { label: '2-3 tỷ', value: [2000000000, 3000000000] },
  { label: '3-5 tỷ', value: [3000000000, 5000000000] },
  { label: '5-7 tỷ', value: [5000000000, 7000000000] },
  { label: 'Trên 7 tỷ', value: [7000000000, 10000000000] },
];
const quickPriceRangesRent = [
  { label: 'Dưới 5tr', value: [0, 5000000] },
  { label: '5-10tr', value: [5000000, 10000000] },
  { label: '10-15tr', value: [10000000, 15000000] },
  { label: '15-20tr', value: [15000000, 20000000] },
  { label: '20-30tr', value: [20000000, 30000000] },
  { label: 'Trên 30tr', value: [30000000, 100000000] },
];

const quickSizeRanges = [
  { label: 'Dưới 30m²', value: [0, 30] },
  { label: '30-50m²', value: [30, 50] },
  { label: '50-70m²', value: [50, 70] },
  { label: '70-100m²', value: [70, 100] },
  { label: '100-150m²', value: [100, 150] },
  { label: 'Trên 150m²', value: [150, 1000] },
];

const sizeDistribution = [
  { size: '0-30', count: 120 },
  { size: '30-50', count: 280 },
  { size: '50-70', count: 320 },
  { size: '70-100', count: 250 },
  { size: '100-150', count: 180 },
  { size: '150-200', count: 120 },
  { size: '200-300', count: 80 },
  { size: '300-500', count: 50 },
  { size: '500-1000', count: 30 },
  { size: '1000+', count: 20 },
];

function SearchFilter() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const isMobile = useIsMobile();

  const { search, location, property, range, actions } = useSearchStore(state => state);

  // Local state for mobile drawer - independent from global store
  const [isMobileDrawerOpen, setIsMobileDrawerOpen] = useState(false);

  const { searchTerm, isDropdownOpen, isSearching } = search;
  const { provinces, districts, wards, selectedProvince, selectedDistrict, selectedWard } =
    location;
  const {
    selectedPropertyTypes,
    selectedTransactionType,
    selectedBedCount,
    selectedBathCount,
    isExactBedMatch,
    selectedLivingRoomCount,
    selectedKitchenCount,
    selectedAmenities,
    selectedDetails,
  } = property;
  const { priceSliderValue, areaSliderValue, minPrice, maxPrice, minArea, maxArea } = range;
  const {
    initializeFromUrl,
    setDropdownOpen,
    selectProvince,
    selectDistrict,
    selectWard,
    buildFilterQuery,
    clearFilters: clearFiltersFromStore,
    setIsSearching,
  } = actions;

  useEffect(() => {
    initializeFromUrl(searchParams);
  }, [searchParams, initializeFromUrl]);

  const handleApplyFilters = () => {
    setIsSearching(true);
    const queryString = buildFilterQuery();
    router.push(`/properties?${queryString}`);
    // Delay setting isSearching to false to allow for UI updates and data fetching simulation
    setTimeout(() => setIsSearching(false), 500);
  };

  const handleClearFilters = () => {
    clearFiltersFromStore();
    router.push('/properties');
    setIsSearching(false);
    setIsMobileDrawerOpen(false);
  };

  const handleLocationChange = (type: 'province' | 'district' | 'ward', value: string) => {
    if (type === 'province') {
      selectProvince(value);
    } else if (type === 'district') {
      selectDistrict(value);
    } else {
      selectWard(value);
      setDropdownOpen('location', false);
      handleApplyFilters();
    }
  };

  const countActiveFilters = () => {
    let count = 0;
    if (searchTerm) count++;
    if (selectedProvince) count++;
    if (selectedPropertyTypes.length > 0) count++;
    if (selectedTransactionType && selectedTransactionType !== 'both') count++;
    const { maxValue } = actions.getCurrentPriceRanges();
    if (priceSliderValue[0] > 0 || priceSliderValue[1] < maxValue) count++;
    if (areaSliderValue[0] > 0 || areaSliderValue[1] < 1000) count++;
    if (selectedBedCount !== 'any') count++;
    if (selectedBathCount !== 'any') count++;
    if (selectedAmenities.length > 0) count++;
    if (selectedDetails.length > 0) count++;
    return count;
  };

  const activeRoomFilters = [
    selectedBedCount,
    selectedBathCount,
    selectedLivingRoomCount,
    selectedKitchenCount,
  ].filter(count => count !== 'any').length;

  const handleApplyAndClose = (dropdown: keyof typeof isDropdownOpen) => {
    handleApplyFilters();
    setDropdownOpen(dropdown, false);
  };

  const handleMobileDrawerApplyAndClose = () => {
    handleApplyFilters();
    setIsMobileDrawerOpen(false);
  };

  return (
    <section className="md:w-full md:max-w-screen md:mx-auto bg-background text-foreground  ">
      <div className="md:ml-4">
        <div className="w-full border-0 bg-card overflow-hidden">
          <div className="md:p-2 md:space-y-2">
            <div className="md:flex md:flex-row gap-2">
              <div className="max-xl:hidden relative w-full md:w-[300px] flex-shrink-0">
                <SearchAutocompleteLocation />
              </div>

              {/* Filter Buttons - Responsive Grid */}
              {/* Location Dropdown */}
              <DropdownMenu onOpenChange={isOpen => setDropdownOpen('location', isOpen)}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-xl:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${
                      selectedProvince
                        ? 'bg-red-50 border-red-200 hover:bg-red-100'
                        : 'text-muted-foreground'
                    }`}
                  >
                    <div className="flex items-center">
                      <MapPin
                        className={`${
                          selectedProvince ? 'text-red-600' : 'text-muted-foreground'
                        } mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${
                          selectedProvince ? 'text-red-600' : 'text-muted-foreground'
                        }`}
                      >
                        {selectedProvince
                          ? provinces.find(p => p.code.toString() === selectedProvince)?.name
                          : 'Chọn địa điểm'}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${
                        selectedProvince ? 'text-red-600' : 'text-muted-foreground'
                      } h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[600px] md:w-[800px] lg:w-[1200px] p-4  ">
                  <div className="space-y-6">
                    <div className="flex gap-2 border-b">
                      <button
                        onClick={() => handleLocationChange('province', '')}
                        onMouseDown={e => e.preventDefault()}
                        className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                          !selectedProvince
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground'
                        }`}
                      >
                        Tỉnh/Thành phố
                      </button>
                      <button
                        onClick={() => {
                          if (selectedProvince) handleLocationChange('district', '');
                        }}
                        onMouseDown={e => e.preventDefault()}
                        className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                          selectedProvince && !selectedDistrict
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground'
                        } ${!selectedProvince ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        Quận/Huyện
                      </button>
                      <button
                        onClick={() => {
                          if (selectedDistrict) handleLocationChange('ward', '');
                        }}
                        onMouseDown={e => e.preventDefault()}
                        className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                          selectedDistrict && !selectedWard
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground'
                        } ${!selectedDistrict ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        Phường/Xã
                      </button>
                    </div>

                    <div className="space-y-6">
                      {!selectedProvince && (
                        <div className="space-y-6">
                          <div className="space-y-4">
                            <h4 className="font-medium text-sm text-muted-foreground">
                              Thành phố trực thuộc trung ương
                            </h4>
                            <div className="grid grid-cols-5 gap-4">
                              {CENTRAL_CITIES.map(city => (
                                <button
                                  key={city.code}
                                  onClick={() => handleLocationChange('province', city.code)}
                                  onMouseDown={e => e.preventDefault()}
                                  className={`relative group rounded-lg overflow-hidden border-2 transition-all ${
                                    selectedProvince === city.code
                                      ? 'border-primary shadow-lg scale-105'
                                      : 'border-transparent hover:border-primary/50'
                                  }`}
                                >
                                  <div className="aspect-[4/3] relative">
                                    <Image
                                      src={city.image}
                                      alt={city.name}
                                      fill
                                      className="object-cover"
                                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                                    <div className="absolute bottom-0 left-0 right-0 p-3 text-white">
                                      <div className="flex items-center gap-2">
                                        <span className="text-xl">{city.icon}</span>
                                        <span className="font-medium">{city.name}</span>
                                      </div>
                                    </div>
                                  </div>
                                </button>
                              ))}
                            </div>
                          </div>

                          <div className="space-y-4">
                            <h4 className="font-medium text-sm text-muted-foreground">
                              Tỉnh/Thành phố khác
                            </h4>
                            <div className="grid grid-cols-5 gap-2">
                              {provinces
                                .filter(
                                  province =>
                                    !CENTRAL_CITIES.find(
                                      city => city.code === province.code.toString()
                                    )
                                )
                                .map(province => (
                                  <button
                                    key={province.code}
                                    onClick={() =>
                                      handleLocationChange('province', province.code.toString())
                                    }
                                    onMouseDown={e => e.preventDefault()}
                                    className={`w-full p-2 rounded-md hover:bg-muted flex items-center gap-2 ${
                                      selectedProvince === province.code.toString()
                                        ? 'bg-primary text-primary-foreground'
                                        : ''
                                    }`}
                                  >
                                    <span className="text-xl">🏘️</span>
                                    <span className="text-sm">{province.name}</span>
                                  </button>
                                ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {selectedProvince && !selectedDistrict && (
                        <div className="space-y-4">
                          <h4 className="font-medium text-sm text-muted-foreground">Quận/Huyện</h4>
                          <div className="grid grid-cols-5 gap-2">
                            {districts.map(district => (
                              <button
                                key={district.code}
                                onClick={() =>
                                  handleLocationChange('district', district.code.toString())
                                }
                                onMouseDown={e => e.preventDefault()}
                                className={`w-full p-2 rounded-md hover:bg-muted flex items-center gap-2 ${
                                  selectedDistrict === district.code.toString()
                                    ? 'bg-primary text-primary-foreground'
                                    : ''
                                }`}
                              >
                                <span className="text-xl">🏢</span>
                                <span className="text-sm">{district.name}</span>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}

                      {selectedDistrict && (
                        <div className="space-y-4">
                          <h4 className="font-medium text-sm text-muted-foreground">Phường/Xã</h4>
                          <div className="grid grid-cols-5 gap-2">
                            {wards.map(ward => (
                              <button
                                key={ward.code}
                                onClick={() => handleLocationChange('ward', ward.code.toString())}
                                onMouseDown={e => e.preventDefault()}
                                className={`w-full p-2 rounded-md hover:bg-muted flex items-center gap-2 ${
                                  selectedWard === ward.code.toString()
                                    ? 'bg-primary text-primary-foreground'
                                    : ''
                                }`}
                              >
                                <span className="text-xl">🏠</span>
                                <span className="text-sm">{ward.name}</span>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Property Type Dropdown */}
              <DropdownMenu onOpenChange={isOpen => setDropdownOpen('propertyType', isOpen)}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-sm:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${
                      selectedPropertyTypes.length > 0
                        ? 'bg-red-50 border-red-200 hover:bg-red-100'
                        : 'text-muted-foreground'
                    }`}
                  >
                    <div className="flex items-center">
                      <Building
                        className={`${
                          selectedPropertyTypes.length > 0
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        } mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${
                          selectedPropertyTypes.length > 0
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        }`}
                      >
                        {selectedPropertyTypes.length > 1
                          ? `${selectedPropertyTypes.length} loại`
                          : selectedPropertyTypes.length === 1
                            ? propertyTypes.find(p => p.id === selectedPropertyTypes[0])?.label
                            : 'Loại nhà'}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${
                        selectedPropertyTypes.length > 0 ? 'text-red-600' : 'text-muted-foreground'
                      } h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[280px]  ">
                  <DropdownMenuLabel className="text-sm font-normal">Loại nhà</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuCheckboxItem
                      checked={selectedPropertyTypes.length === propertyTypes.length}
                      onCheckedChange={() => actions.selectAllPropertyTypes()}
                      onSelect={e => e.preventDefault()}
                      className="flex items-center py-2"
                    >
                      <div className="flex items-center space-x-2 w-full">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>Chọn tất cả</span>
                      </div>
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuSeparator />
                    {propertyTypes.map(type => (
                      <DropdownMenuCheckboxItem
                        key={type.id}
                        checked={selectedPropertyTypes.includes(type.id)}
                        onCheckedChange={() => actions.togglePropertyType(type.id)}
                        onSelect={e => e.preventDefault()}
                        className="flex items-center py-2"
                      >
                        <div className="flex items-center space-x-2 w-full">
                          <type.icon className="h-4 w-4 text-muted-foreground" />
                          <span>{type.label}</span>
                        </div>
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button
                      className="w-full bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                      onClick={() => handleApplyAndClose('propertyType')}
                    >
                      Áp dụng
                    </Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Transaction Type Dropdown */}
              <DropdownMenu onOpenChange={isOpen => setDropdownOpen('transactionType', isOpen)}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-xl:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${
                      selectedTransactionType && selectedTransactionType !== 'both'
                        ? 'bg-red-50 border-red-200 hover:bg-red-100'
                        : 'text-muted-foreground'
                    }`}
                  >
                    <div className="flex items-center">
                      <Home
                        className={`${
                          selectedTransactionType && selectedTransactionType !== 'both'
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        } mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${
                          selectedTransactionType && selectedTransactionType !== 'both'
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        }`}
                      >
                        {(selectedTransactionType &&
                          selectedTransactionType !== 'both' &&
                          transactionTypesData.find(t => t.id === selectedTransactionType)
                            ?.label) ||
                          'Mua/Thuê'}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${
                        selectedTransactionType && selectedTransactionType !== 'both'
                          ? 'text-red-600'
                          : 'text-muted-foreground'
                      } h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[280px]  ">
                  <DropdownMenuLabel className="text-sm font-normal">Mua/Thuê</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuRadioGroup
                      value={selectedTransactionType || 'both'}
                      onValueChange={value =>
                        actions.setTransactionType(value as TransactionType | 'both')
                      }
                    >
                      {transactionTypesData.map(type => (
                        <DropdownMenuRadioItem
                          key={type.id}
                          value={type.id}
                          onSelect={e => e.preventDefault()}
                          className="flex items-center py-2"
                        >
                          <div className="flex items-center space-x-2 w-full">
                            <type.icon className="h-4 w-4 text-muted-foreground" />
                            <span>{type.label}</span>
                          </div>
                        </DropdownMenuRadioItem>
                      ))}
                    </DropdownMenuRadioGroup>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button
                      className="w-full bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                      onClick={() => handleApplyAndClose('transactionType')}
                    >
                      Áp dụng
                    </Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Price Range Dropdown */}
              <DropdownMenu onOpenChange={isOpen => setDropdownOpen('price', isOpen)}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-md:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${
                      priceSliderValue[0] > 0 ||
                      priceSliderValue[1] < actions.getCurrentPriceRanges().maxValue
                        ? 'bg-red-50 border-red-200 hover:bg-red-100'
                        : 'text-muted-foreground'
                    }`}
                  >
                    <div className="flex items-center">
                      <DollarSign
                        className={`${
                          priceSliderValue[0] > 0 ||
                          priceSliderValue[1] < actions.getCurrentPriceRanges().maxValue
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        } mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${
                          priceSliderValue[0] > 0 ||
                          priceSliderValue[1] < actions.getCurrentPriceRanges().maxValue
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        }`}
                      >
                        {actions.formatPriceDisplay(priceSliderValue[0], priceSliderValue[1])}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${
                        priceSliderValue[0] > 0 ||
                        priceSliderValue[1] < actions.getCurrentPriceRanges().maxValue
                          ? 'text-red-600'
                          : 'text-muted-foreground'
                      } h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[400px] p-4">
                  <DropdownMenuLabel className="text-sm font-normal">
                    {selectedTransactionType === TransactionType.FOR_RENT ? 'Giá thuê' : 'Giá bán'}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <div className="space-y-4   py-2">
                      {/* Price Inputs */}
                      <div className="flex justify-between items-center gap-2">
                        <div className="flex flex-col w-1/2">
                          <label className="text-sm text-muted-foreground mb-1">
                            {selectedTransactionType === TransactionType.FOR_RENT
                              ? 'Giá thuê tối thiểu'
                              : 'Giá thấp nhất'}
                          </label>
                          <div className="relative">
                            <Input
                              value={minPrice || ''}
                              onChange={e =>
                                actions.setMinPrice(e.target.value.replace(/[^0-9]/g, ''))
                              }
                              type="text"
                              placeholder="Từ"
                              inputMode="numeric"
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                              {selectedTransactionType === TransactionType.FOR_RENT
                                ? 'VND/tháng'
                                : 'VND'}
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-col w-1/2">
                          <label className="text-sm text-muted-foreground mb-1">
                            {selectedTransactionType === TransactionType.FOR_RENT
                              ? 'Giá thuê tối đa'
                              : 'Giá cao nhất'}
                          </label>
                          <div className="relative">
                            <Input
                              value={maxPrice || ''}
                              onChange={e =>
                                actions.setMaxPrice(e.target.value.replace(/[^0-9]/g, ''))
                              }
                              type="text"
                              placeholder="Đến"
                              inputMode="numeric"
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                              {selectedTransactionType === TransactionType.FOR_RENT
                                ? 'VND/tháng'
                                : 'VND'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Price Distribution Chart */}
                      <div className="h-[100px] w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart
                            data={
                              selectedTransactionType === TransactionType.FOR_RENT
                                ? priceDistributionRent
                                : priceDistributionSale
                            }
                            margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                          >
                            <defs>
                              <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                              </linearGradient>
                            </defs>
                            <XAxis
                              dataKey="price"
                              tickFormatter={value => {
                                const isRent = selectedTransactionType === TransactionType.FOR_RENT;
                                return `${value}${isRent ? 'tr' : 'tỷ'}`;
                              }}
                              tick={{ fontSize: 10 }}
                              axisLine={false}
                              tickLine={false}
                            />
                            <YAxis hide={true} domain={[0, 'dataMax']} />
                            <Tooltip
                              content={({ active, payload }) => {
                                if (active && payload && payload.length) {
                                  const isRent =
                                    selectedTransactionType === TransactionType.FOR_RENT;
                                  const isLastItem =
                                    payload[0].payload.price === (isRent ? '70+' : '9-10');
                                  const unit = isRent ? 'tr' : 'tỷ';
                                  return (
                                    <div className="bg-white p-2 border rounded shadow-sm">
                                      <p className="text-sm font-medium">
                                        {payload[0].payload.price}
                                        {unit}
                                        {isLastItem ? '+' : ''}: {payload[0].value} bất động sản
                                      </p>
                                    </div>
                                  );
                                }
                                return null;
                              }}
                            />
                            <Area
                              type="monotone"
                              dataKey="count"
                              stroke="#ef4444"
                              strokeWidth={2}
                              fill="url(#colorCount)"
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>

                      {/* Price Slider */}
                      <div className="px-2">
                        <Slider
                          min={0}
                          max={actions.getCurrentPriceRanges().maxValue}
                          step={actions.getCurrentPriceRanges().step}
                          value={priceSliderValue}
                          onValueChange={actions.setPriceSliderValue}
                          className="relative flex w-full touch-none select-none items-center"
                        />
                        <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                          <span>{actions.formatPriceLabel(priceSliderValue[0])}</span>
                          <span>
                            {actions.formatPriceLabel(
                              priceSliderValue[1],
                              priceSliderValue[1] === actions.getCurrentPriceRanges().maxValue
                            )}
                          </span>
                        </div>
                      </div>

                      {/* Quick Price Ranges */}
                      <div className="grid grid-cols-2 gap-2">
                        {(selectedTransactionType === TransactionType.FOR_RENT
                          ? quickPriceRangesRent
                          : quickPriceRangesSale
                        ).map(range => (
                          <Button
                            key={range.label}
                            variant="outline"
                            className="text-sm h-8"
                            onClick={() => {
                              actions.setPriceSliderValue(range.value as [number, number]);
                              actions.setMinPrice(range.value[0].toString());
                              actions.setMaxPrice(range.value[1].toString());
                            }}
                          >
                            {range.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <div className="flex justify-between">
                      <Button
                        className="text-red-600 hover:text-red-700"
                        variant="ghost"
                        onClick={actions.resetPrice}
                      >
                        Đặt lại
                      </Button>
                      <Button
                        className="bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                        onClick={() => handleApplyAndClose('price')}
                      >
                        Áp dụng
                      </Button>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Property Size Dropdown */}
              <DropdownMenu onOpenChange={isOpen => setDropdownOpen('area', isOpen)}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-xl:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${
                      areaSliderValue[0] > 0 || areaSliderValue[1] < 1000
                        ? 'bg-red-50 border-red-200 hover:bg-red-100'
                        : 'text-muted-foreground'
                    }`}
                  >
                    <div className="flex items-center">
                      <Ruler
                        className={`${
                          areaSliderValue[0] > 0 || areaSliderValue[1] < 1000
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        } mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${
                          areaSliderValue[0] > 0 || areaSliderValue[1] < 1000
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        }`}
                      >
                        {actions.formatSizeDisplay(areaSliderValue[0], areaSliderValue[1])}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${
                        areaSliderValue[0] > 0 || areaSliderValue[1] < 1000
                          ? 'text-red-600'
                          : 'text-muted-foreground'
                      } h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[400px] p-4">
                  <DropdownMenuLabel className="text-sm font-normal">Diện tích</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <div className="space-y-4   pb-2">
                      {/* Size Inputs */}
                      <div className="flex justify-between items-center gap-2">
                        <div className="flex flex-col w-1/2">
                          <label className="text-sm text-muted-foreground mb-1">
                            Diện tích tối thiểu
                          </label>
                          <div className="relative">
                            <Input
                              value={minArea || ''}
                              onChange={e =>
                                actions.setMinArea(e.target.value.replace(/[^0-9]/g, ''))
                              }
                              type="text"
                              placeholder="Từ"
                              inputMode="numeric"
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                              m²
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-col w-1/2">
                          <label className="text-sm text-muted-foreground mb-1">
                            Diện tích tối đa
                          </label>
                          <div className="relative">
                            <Input
                              value={maxArea || ''}
                              onChange={e =>
                                actions.setMaxArea(e.target.value.replace(/[^0-9]/g, ''))
                              }
                              type="text"
                              placeholder="Đến"
                              inputMode="numeric"
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                              m²
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Size Distribution Chart */}
                      <div className="h-[100px] w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart
                            data={sizeDistribution}
                            margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                          >
                            <defs>
                              <linearGradient id="colorSizeCount" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                              </linearGradient>
                            </defs>
                            <XAxis
                              dataKey="size"
                              tickFormatter={value => `${value}m²`}
                              tick={{ fontSize: 10 }}
                              axisLine={false}
                              tickLine={false}
                            />
                            <YAxis hide={true} domain={[0, 'dataMax']} />
                            <Tooltip
                              content={({ active, payload }) => {
                                if (active && payload && payload.length) {
                                  const isLastItem = payload[0].payload.size === '1000+';
                                  return (
                                    <div className="bg-white p-2 border rounded shadow-sm">
                                      <p className="text-sm font-medium">
                                        {payload[0].payload.size}
                                        {isLastItem ? '+' : ''}: {payload[0].value} bất động sản
                                      </p>
                                    </div>
                                  );
                                }
                                return null;
                              }}
                            />
                            <Area
                              type="monotone"
                              dataKey="count"
                              stroke="#ef4444"
                              strokeWidth={2}
                              fill="url(#colorSizeCount)"
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>

                      {/* Size Slider */}
                      <div className="px-2">
                        <Slider
                          min={0}
                          max={1000}
                          step={5}
                          value={areaSliderValue}
                          onValueChange={actions.setAreaSliderValue}
                          className="relative flex w-full touch-none select-none items-center"
                        />
                        <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                          <span>{actions.formatSizeLabel(areaSliderValue[0])}</span>
                          <span>
                            {actions.formatSizeLabel(
                              areaSliderValue[1],
                              areaSliderValue[1] === 1000
                            )}
                          </span>
                        </div>
                      </div>

                      {/* Quick Size Ranges */}
                      <div className="grid grid-cols-2 gap-2">
                        {quickSizeRanges.map(range => (
                          <Button
                            key={range.label}
                            variant="outline"
                            className="text-sm h-8"
                            onClick={() => {
                              actions.setAreaSliderValue(range.value as [number, number]);
                              actions.setMinArea(range.value[0].toString());
                              actions.setMaxArea(range.value[1].toString());
                            }}
                          >
                            {range.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <div className="flex justify-between">
                      <Button
                        className="text-red-600 hover:text-red-700"
                        variant="ghost"
                        onClick={actions.resetArea}
                      >
                        Đặt lại
                      </Button>
                      <Button
                        className="bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                        onClick={() => handleApplyAndClose('area')}
                      >
                        Áp dụng
                      </Button>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Bed Count Dropdown */}
              <DropdownMenu onOpenChange={isOpen => setDropdownOpen('rooms', isOpen)}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-md:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${
                      activeRoomFilters > 0
                        ? 'bg-red-50 border-red-200 hover:bg-red-100'
                        : 'text-muted-foreground'
                    }`}
                  >
                    <div className="flex items-center">
                      <BedDouble
                        className={`${
                          activeRoomFilters > 0 ? 'text-red-600' : 'text-muted-foreground'
                        } mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${
                          activeRoomFilters > 0 ? 'text-red-600' : 'text-muted-foreground'
                        }`}
                      >
                        {activeRoomFilters > 0 ? `Số phòng (${activeRoomFilters})` : 'Số phòng'}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${
                        activeRoomFilters > 0 ? 'text-red-600' : 'text-muted-foreground'
                      } h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[400px]">
                  <DropdownMenuLabel className="text-sm font-normal">Số phòng</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <div className="p-4 space-y-6  ">
                      {/* Bed Count Section */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">Số phòng ngủ</h4>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="exact-bed-match"
                              checked={isExactBedMatch}
                              onCheckedChange={() => actions.toggleExactBedMatch()}
                            />
                            <label
                              htmlFor="exact-bed-match"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              Chính xác số phòng
                            </label>
                          </div>
                        </div>
                        <RadioGroup.Root
                          value={selectedBedCount}
                          onValueChange={actions.setBedCount}
                          className="max-w-sm w-full grid grid-cols-3 gap-3"
                        >
                          {(isExactBedMatch ? exactBedOptions : bedOptions).map(option => (
                            <RadioGroup.Item
                              key={option.id}
                              value={option.id}
                              className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                            >
                              <span className="font-medium text-sm">{option.label}</span>
                            </RadioGroup.Item>
                          ))}
                        </RadioGroup.Root>
                      </div>

                      <DropdownMenuSeparator />

                      {/* Bath Count Section */}
                      <div className="space-y-4">
                        <h4 className="font-medium text-sm">Số phòng tắm</h4>
                        <RadioGroup.Root
                          value={selectedBathCount}
                          onValueChange={actions.setBathCount}
                          className="max-w-sm w-full grid grid-cols-3 gap-3"
                        >
                          {bathOptions.map(option => (
                            <RadioGroup.Item
                              key={option.id}
                              value={option.id}
                              className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                            >
                              <span className="font-medium text-sm">{option.label}</span>
                            </RadioGroup.Item>
                          ))}
                        </RadioGroup.Root>
                      </div>

                      <DropdownMenuSeparator />

                      <div className="space-y-4">
                        <h4 className="font-medium text-sm">Số phòng khách</h4>
                        <RadioGroup.Root
                          value={selectedLivingRoomCount}
                          onValueChange={actions.setLivingRoomCount}
                          className="max-w-sm w-full grid grid-cols-3 gap-3"
                        >
                          {livingRoomOptions.map(option => (
                            <RadioGroup.Item
                              key={option.id}
                              value={option.id}
                              className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                            >
                              <span className="font-medium text-sm">{option.label}</span>
                            </RadioGroup.Item>
                          ))}
                        </RadioGroup.Root>
                      </div>

                      <DropdownMenuSeparator />

                      <div className="space-y-4">
                        <h4 className="font-medium text-sm">Số nhà bếp</h4>
                        <RadioGroup.Root
                          value={selectedKitchenCount}
                          onValueChange={actions.setKitchenCount}
                          className="max-w-sm w-full grid grid-cols-3 gap-3"
                        >
                          {kitchenOptions.map(option => (
                            <RadioGroup.Item
                              key={option.id}
                              value={option.id}
                              className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                            >
                              <span className="font-medium text-sm">{option.label}</span>
                            </RadioGroup.Item>
                          ))}
                        </RadioGroup.Root>
                      </div>
                    </div>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button
                      className="w-full bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                      onClick={() => handleApplyAndClose('rooms')}
                    >
                      Áp dụng
                    </Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              <div className="sm:col-span-3 md:col-span-4 md:flex md:flex-row md:gap-2">
                <Button
                  className="max-md:hidden w-full lg:w-auto bg-red-600 hover:bg-red-700 focus-visible:ring-0 focus-visible:outline-none"
                  onClick={handleApplyFilters}
                  disabled={isSearching}
                >
                  {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Tìm kiếm'}
                </Button>

                <SaveSearchButton
                  className="max-md:hidden lg:w-auto"
                  variant="outline"
                  size="default"
                />

                {isMobile ? (
                  <Drawer open={isMobileDrawerOpen} onOpenChange={setIsMobileDrawerOpen}>
                    <DrawerTrigger asChild>
                      <Button
                        variant="outline"
                        className="max-md:h-10 max-md:w-10 w-full max-md:rounded-full lg:w-auto"
                        onSelect={e => e.preventDefault()}
                      >
                        <SlidersHorizontal className="h-4 w-4" />
                      </Button>
                    </DrawerTrigger>
                    <DrawerContent className="h-[85vh]">
                      <DrawerHeader className="border-b">
                        <DrawerTitle className="text-base">Bộ lọc tìm kiếm</DrawerTitle>
                      </DrawerHeader>
                      <ScrollArea className="flex-1 overflow-y-auto">
                        <div className="flex flex-col gap-2.5 p-4 space-y-6">
                          {/* Property Type Section */}
                          <div className="space-y-4">
                            <h3 className="font-medium text-sm">Loại bất động sản</h3>
                            <div className="w-full grid grid-cols-3 gap-3">
                              {propertyTypes.map(type => (
                                <CheckboxPrimitive.Root
                                  key={type.id}
                                  onCheckedChange={() => actions.togglePropertyType(type.id)}
                                  checked={selectedPropertyTypes.includes(type.id)}
                                  className="relative ring-[1px] ring-border rounded-lg px-4 py-3 text-start text-foreground data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:text-red-600"
                                >
                                  <type.icon className="mb-3 size-4" />
                                  <span className="font-medium text-xs tracking-tight">
                                    {type.label}
                                  </span>
                                  <CheckboxPrimitive.Indicator className="absolute top-2 right-2">
                                    <CircleCheck className="fill-red-600 text-white" />
                                  </CheckboxPrimitive.Indicator>
                                </CheckboxPrimitive.Root>
                              ))}
                            </div>
                          </div>
                          <Separator />

                          {/* Transaction Type Section */}
                          <div className="space-y-4">
                            <h3 className="font-medium text-sm">Loại giao dịch</h3>
                            <RadioGroup.Root
                              value={selectedTransactionType || 'both'}
                              onValueChange={value =>
                                actions.setTransactionType(value as TransactionType | 'both')
                              }
                              className="grid grid-cols-3 gap-2"
                            >
                              {transactionTypesData.map(type => (
                                <RadioGroup.Item
                                  key={type.id}
                                  value={type.id}
                                  className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                                >
                                  <div className="flex flex-col items-center gap-2">
                                    <type.icon className="h-5 w-5" />
                                    <span className="font-medium text-sm">{type.label}</span>
                                  </div>
                                </RadioGroup.Item>
                              ))}
                            </RadioGroup.Root>
                          </div>

                          <Separator />

                          {/* Price Range Section */}
                          <div className="space-y-4">
                            <h3 className="font-medium text-sm">
                              {selectedTransactionType === TransactionType.FOR_RENT
                                ? 'Giá thuê'
                                : 'Giá bán'}
                            </h3>
                            <div className="space-y-4   py-2">
                              {/* Price Inputs */}
                              <div className="flex justify-between items-center gap-2">
                                <div className="flex flex-col w-1/2">
                                  <label className="text-xs text-muted-foreground mb-1">
                                    {selectedTransactionType === TransactionType.FOR_RENT
                                      ? 'Giá thuê tối thiểu'
                                      : 'Giá thấp nhất'}
                                  </label>
                                  <div className="relative">
                                    <Input
                                      value={minPrice || ''}
                                      onChange={e =>
                                        actions.setMinPrice(e.target.value.replace(/[^0-9]/g, ''))
                                      }
                                      type="text"
                                      placeholder="Từ"
                                      inputMode="numeric"
                                      className="pr-8"
                                    />
                                    <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                      {selectedTransactionType === TransactionType.FOR_RENT
                                        ? 'VND/tháng'
                                        : 'VND'}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex flex-col w-1/2">
                                  <label className="text-xs text-muted-foreground mb-1">
                                    {selectedTransactionType === TransactionType.FOR_RENT
                                      ? 'Giá thuê tối đa'
                                      : 'Giá cao nhất'}
                                  </label>
                                  <div className="relative">
                                    <Input
                                      value={maxPrice || ''}
                                      onChange={e =>
                                        actions.setMaxPrice(e.target.value.replace(/[^0-9]/g, ''))
                                      }
                                      type="text"
                                      placeholder="Đến"
                                      inputMode="numeric"
                                      className="pr-8"
                                    />
                                    <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                      {selectedTransactionType === TransactionType.FOR_RENT
                                        ? 'VND/tháng'
                                        : 'VND'}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Price Distribution Chart */}
                              <div className="h-[100px] w-full">
                                <ResponsiveContainer width="100%" height="100%">
                                  <AreaChart
                                    data={
                                      selectedTransactionType === TransactionType.FOR_RENT
                                        ? priceDistributionRent
                                        : priceDistributionSale
                                    }
                                    margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                                  >
                                    <defs>
                                      <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                        <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                                      </linearGradient>
                                    </defs>
                                    <XAxis
                                      dataKey="price"
                                      tickFormatter={value => {
                                        const isRent =
                                          selectedTransactionType === TransactionType.FOR_RENT;
                                        return `${value}${isRent ? 'tr' : 'tỷ'}`;
                                      }}
                                      tick={{ fontSize: 10 }}
                                      axisLine={false}
                                      tickLine={false}
                                    />
                                    <YAxis hide={true} domain={[0, 'dataMax']} />
                                    <Tooltip
                                      content={({ active, payload }) => {
                                        if (active && payload && payload.length) {
                                          const isRent =
                                            selectedTransactionType === TransactionType.FOR_RENT;
                                          const isLastItem =
                                            payload[0].payload.price === (isRent ? '70+' : '9-10');
                                          const unit = isRent ? 'tr' : 'tỷ';
                                          return (
                                            <div className="bg-white p-2 border rounded shadow-sm">
                                              <p className="text-sm font-medium">
                                                {payload[0].payload.price}
                                                {unit}
                                                {isLastItem ? '+' : ''}: {payload[0].value} bất động
                                                sản
                                              </p>
                                            </div>
                                          );
                                        }
                                        return null;
                                      }}
                                    />
                                    <Area
                                      type="monotone"
                                      dataKey="count"
                                      stroke="#ef4444"
                                      strokeWidth={2}
                                      fill="url(#colorCount)"
                                    />
                                  </AreaChart>
                                </ResponsiveContainer>
                              </div>

                              {/* Price Slider */}
                              <div className="px-2">
                                <Slider
                                  min={0}
                                  max={actions.getCurrentPriceRanges().maxValue}
                                  step={actions.getCurrentPriceRanges().step}
                                  value={priceSliderValue}
                                  onValueChange={actions.setPriceSliderValue}
                                  className="relative flex w-full touch-none select-none items-center"
                                />
                                <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                                  <span>{actions.formatPriceLabel(priceSliderValue[0])}</span>
                                  <span>
                                    {actions.formatPriceLabel(
                                      priceSliderValue[1],
                                      priceSliderValue[1] ===
                                        actions.getCurrentPriceRanges().maxValue
                                    )}
                                  </span>
                                </div>
                              </div>

                              {/* Quick Price Ranges */}
                              <div className="grid grid-cols-2 gap-2">
                                {(selectedTransactionType === TransactionType.FOR_RENT
                                  ? quickPriceRangesRent
                                  : quickPriceRangesSale
                                ).map(range => (
                                  <Button
                                    key={range.label}
                                    variant="outline"
                                    className="text-xs h-10"
                                    onClick={() => {
                                      actions.setPriceSliderValue(range.value as [number, number]);
                                      actions.setMinPrice(range.value[0].toString());
                                      actions.setMaxPrice(range.value[1].toString());
                                    }}
                                  >
                                    {range.label}
                                  </Button>
                                ))}
                              </div>
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-4 ">
                            <h3 className="font-medium text-sm">Diện tích</h3>
                            {/* Size Inputs */}
                            <div className="flex justify-between items-center gap-2">
                              <div className="flex flex-col w-1/2">
                                <label className="text-xs text-muted-foreground mb-1">
                                  Diện tích tối thiểu
                                </label>
                                <div className="relative">
                                  <Input
                                    value={minArea || ''}
                                    onChange={e =>
                                      actions.setMinArea(e.target.value.replace(/[^0-9]/g, ''))
                                    }
                                    type="text"
                                    placeholder="Từ"
                                    inputMode="numeric"
                                    className="pr-8"
                                  />
                                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                    m²
                                  </span>
                                </div>
                              </div>
                              <div className="flex flex-col w-1/2">
                                <label className="text-xs text-muted-foreground mb-1">
                                  Diện tích tối đa
                                </label>
                                <div className="relative">
                                  <Input
                                    value={maxArea || ''}
                                    onChange={e =>
                                      actions.setMaxArea(e.target.value.replace(/[^0-9]/g, ''))
                                    }
                                    type="text"
                                    placeholder="Đến"
                                    inputMode="numeric"
                                    className="pr-8"
                                  />
                                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                    m²
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Size Distribution Chart */}
                            <div className="h-[100px] w-full">
                              <ResponsiveContainer width="100%" height="100%">
                                <AreaChart
                                  data={sizeDistribution}
                                  margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                                >
                                  <defs>
                                    <linearGradient id="colorSizeCount" x1="0" y1="0" x2="0" y2="1">
                                      <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                      <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                                    </linearGradient>
                                  </defs>
                                  <XAxis
                                    dataKey="size"
                                    tickFormatter={value => `${value}m²`}
                                    tick={{ fontSize: 10 }}
                                    axisLine={false}
                                    tickLine={false}
                                  />
                                  <YAxis hide={true} domain={[0, 'dataMax']} />
                                  <Tooltip
                                    content={({ active, payload }) => {
                                      if (active && payload && payload.length) {
                                        const isLastItem = payload[0].payload.size === '1000+';
                                        return (
                                          <div className="bg-white p-2 border rounded shadow-sm">
                                            <p className="text-sm font-medium">
                                              {payload[0].payload.size}
                                              {isLastItem ? '+' : ''}: {payload[0].value} bất động
                                              sản
                                            </p>
                                          </div>
                                        );
                                      }
                                      return null;
                                    }}
                                  />
                                  <Area
                                    type="monotone"
                                    dataKey="count"
                                    stroke="#ef4444"
                                    strokeWidth={2}
                                    fill="url(#colorSizeCount)"
                                  />
                                </AreaChart>
                              </ResponsiveContainer>
                            </div>

                            {/* Size Slider */}
                            <div className="px-2">
                              <Slider
                                min={0}
                                max={1000}
                                step={5}
                                value={areaSliderValue}
                                onValueChange={actions.setAreaSliderValue}
                                className="relative flex w-full touch-none select-none items-center"
                              />
                              <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                                <span>{actions.formatSizeLabel(areaSliderValue[0])}</span>
                                <span>
                                  {actions.formatSizeLabel(
                                    areaSliderValue[1],
                                    areaSliderValue[1] === 1000
                                  )}
                                </span>
                              </div>
                            </div>

                            {/* Quick Size Ranges */}
                            <div className="grid grid-cols-2 gap-2">
                              {quickSizeRanges.map(range => (
                                <Button
                                  key={range.label}
                                  variant="outline"
                                  className="text-xs h-10"
                                  onClick={() => {
                                    actions.setAreaSliderValue(range.value as [number, number]);
                                    actions.setMinArea(range.value[0].toString());
                                    actions.setMaxArea(range.value[1].toString());
                                  }}
                                >
                                  {range.label}
                                </Button>
                              ))}
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-6">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium text-sm">Số phòng ngủ</h4>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="exact-bed-match-sheet"
                                  checked={isExactBedMatch}
                                  onCheckedChange={() => actions.toggleExactBedMatch()}
                                />
                                <label
                                  htmlFor="exact-bed-match-sheet"
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  Chính xác số phòng
                                </label>
                              </div>
                            </div>
                            <RadioGroup.Root
                              value={selectedBedCount}
                              onValueChange={actions.setBedCount}
                              className="grid grid-cols-3 gap-2"
                            >
                              {(isExactBedMatch ? exactBedOptions : bedOptions).map(option => (
                                <RadioGroup.Item
                                  key={option.id}
                                  value={option.id}
                                  className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm">{option.label}</span>
                                </RadioGroup.Item>
                              ))}
                            </RadioGroup.Root>
                          </div>

                          <Separator />

                          <div className="space-y-4">
                            <h3 className="font-medium text-sm">Số phòng tắm</h3>
                            <RadioGroup.Root
                              value={selectedBathCount}
                              onValueChange={actions.setBathCount}
                              className="grid grid-cols-3 gap-2"
                            >
                              {bathOptions.map(option => (
                                <RadioGroup.Item
                                  key={option.id}
                                  value={option.id}
                                  className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm">{option.label}</span>
                                </RadioGroup.Item>
                              ))}
                            </RadioGroup.Root>
                          </div>

                          <Separator />

                          <div className="space-y-4">
                            <h3 className="font-medium text-sm">Tiện ích</h3>
                            <div className="w-full grid grid-cols-2 md:grid-cols-3 gap-3">
                              {amenityOptions.map(option => (
                                <CheckboxPrimitive.Root
                                  key={option.id}
                                  onCheckedChange={() => actions.toggleAmenity(option.id)}
                                  checked={selectedAmenities.includes(option.id)}
                                  className="relative ring-[1px] ring-border rounded-lg p-3 text-start text-foreground data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm tracking-tight">
                                    {option.label}
                                  </span>
                                  <CheckboxPrimitive.Indicator className="absolute top-2 right-2">
                                    <CircleCheck className="fill-red-600 text-white" />
                                  </CheckboxPrimitive.Indicator>
                                </CheckboxPrimitive.Root>
                              ))}
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-4">
                            <h3 className="font-medium text-sm">Chi tiết bất động sản</h3>
                            <div className="w-full grid grid-cols-2 md:grid-cols-3 gap-3">
                              {detailOptions.map(option => (
                                <CheckboxPrimitive.Root
                                  key={option.id}
                                  onCheckedChange={() => actions.toggleDetail(option.id)}
                                  checked={selectedDetails.includes(option.id)}
                                  className="relative ring-[1px] ring-border rounded-lg p-3 text-start text-foreground data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm tracking-tight">
                                    {option.label}
                                  </span>
                                  <CheckboxPrimitive.Indicator className="absolute top-2 right-2">
                                    <CircleCheck className="fill-red-600 text-white" />
                                  </CheckboxPrimitive.Indicator>
                                </CheckboxPrimitive.Root>
                              ))}
                            </div>
                          </div>
                        </div>
                      </ScrollArea>
                      <DrawerFooter className="sticky bottom-0 bg-background border-t p-4">
                        <div className="flex justify-between gap-2">
                          <Button
                            variant="outline"
                            className="flex-1 text-red-600 hover:text-red-700"
                            onClick={handleClearFilters}
                          >
                            Đặt lại
                          </Button>
                          <Button
                            className="flex-1 bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                            onClick={handleMobileDrawerApplyAndClose}
                          >
                            Xem kết quả
                          </Button>
                        </div>
                      </DrawerFooter>
                    </DrawerContent>
                  </Drawer>
                ) : (
                  <Sheet
                    open={isDropdownOpen.moreFilters}
                    onOpenChange={isOpen => setDropdownOpen('moreFilters', isOpen)}
                  >
                    <SheetTrigger asChild>
                      <Button
                        variant="outline"
                        className="max-md:h-10 max-md:w-10 w-full max-md:rounded-full lg:w-auto"
                        onSelect={e => e.preventDefault()}
                      >
                        <SlidersHorizontal className="h-4 w-4" />
                      </Button>
                    </SheetTrigger>
                    <SheetContent className="w-full sm:max-w-xl">
                      <SheetHeader>
                        <SheetTitle>Bộ lọc tìm kiếm</SheetTitle>
                      </SheetHeader>
                      <ScrollArea className="h-full px-2">
                        <div className="flex flex-col gap-2.5 p-4 space-y-6">
                          {/* Property Type Section */}
                          <div className="space-y-4">
                            <h3 className="font-medium text-base">Loại bất động sản</h3>
                            <div className="w-full grid grid-cols-2 md:grid-cols-4 gap-3">
                              {propertyTypes.map(type => (
                                <CheckboxPrimitive.Root
                                  key={type.id}
                                  onCheckedChange={() => actions.togglePropertyType(type.id)}
                                  checked={selectedPropertyTypes.includes(type.id)}
                                  className="relative ring-[1px] ring-border rounded-lg px-4 py-3 text-start text-foreground data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:text-red-600"
                                >
                                  <type.icon className="mb-3 size-4" />
                                  <span className="font-medium text-sm tracking-tight">
                                    {type.label}
                                  </span>
                                  <CheckboxPrimitive.Indicator className="absolute top-2 right-2">
                                    <CircleCheck className="fill-red-600 text-white" />
                                  </CheckboxPrimitive.Indicator>
                                </CheckboxPrimitive.Root>
                              ))}
                            </div>
                          </div>
                          <Separator />

                          {/* Transaction Type Section */}
                          <div className="space-y-4">
                            <h3 className="font-medium text-base">Loại giao dịch</h3>
                            <RadioGroup.Root
                              value={selectedTransactionType || 'both'}
                              onValueChange={value =>
                                actions.setTransactionType(value as TransactionType | 'both')
                              }
                              className="grid grid-cols-3 gap-2"
                            >
                              {transactionTypesData.map(type => (
                                <RadioGroup.Item
                                  key={type.id}
                                  value={type.id}
                                  className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                                >
                                  <div className="flex flex-col items-center gap-2">
                                    <type.icon className="h-5 w-5" />
                                    <span className="font-medium text-sm">{type.label}</span>
                                  </div>
                                </RadioGroup.Item>
                              ))}
                            </RadioGroup.Root>
                          </div>

                          <Separator />

                          {/* Price Range Section */}
                          <div className="space-y-4">
                            <h3 className="font-medium text-base">
                              {selectedTransactionType === TransactionType.FOR_RENT
                                ? 'Giá thuê'
                                : 'Giá bán'}
                            </h3>
                            <div className="space-y-4   py-2">
                              {/* Price Inputs */}
                              <div className="flex justify-between items-center gap-2">
                                <div className="flex flex-col w-1/2">
                                  <label className="text-sm text-muted-foreground mb-1">
                                    {selectedTransactionType === TransactionType.FOR_RENT
                                      ? 'Giá thuê tối thiểu'
                                      : 'Giá thấp nhất'}
                                  </label>
                                  <div className="relative">
                                    <Input
                                      value={minPrice || ''}
                                      onChange={e =>
                                        actions.setMinPrice(e.target.value.replace(/[^0-9]/g, ''))
                                      }
                                      type="text"
                                      placeholder="Từ"
                                      inputMode="numeric"
                                      className="pr-8"
                                    />
                                    <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                      {selectedTransactionType === TransactionType.FOR_RENT
                                        ? 'VND/tháng'
                                        : 'VND'}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex flex-col w-1/2">
                                  <label className="text-sm text-muted-foreground mb-1">
                                    {selectedTransactionType === TransactionType.FOR_RENT
                                      ? 'Giá thuê tối đa'
                                      : 'Giá cao nhất'}
                                  </label>
                                  <div className="relative">
                                    <Input
                                      value={maxPrice || ''}
                                      onChange={e =>
                                        actions.setMaxPrice(e.target.value.replace(/[^0-9]/g, ''))
                                      }
                                      type="text"
                                      placeholder="Đến"
                                      inputMode="numeric"
                                      className="pr-8"
                                    />
                                    <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                      {selectedTransactionType === TransactionType.FOR_RENT
                                        ? 'VND/tháng'
                                        : 'VND'}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Price Distribution Chart */}
                              <div className="h-[100px] w-full">
                                <ResponsiveContainer width="100%" height="100%">
                                  <AreaChart
                                    data={
                                      selectedTransactionType === TransactionType.FOR_RENT
                                        ? priceDistributionRent
                                        : priceDistributionSale
                                    }
                                    margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                                  >
                                    <defs>
                                      <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                        <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                                      </linearGradient>
                                    </defs>
                                    <XAxis
                                      dataKey="price"
                                      tickFormatter={value => {
                                        const isRent =
                                          selectedTransactionType === TransactionType.FOR_RENT;
                                        return `${value}${isRent ? 'tr' : 'tỷ'}`;
                                      }}
                                      tick={{ fontSize: 10 }}
                                      axisLine={false}
                                      tickLine={false}
                                    />
                                    <YAxis hide={true} domain={[0, 'dataMax']} />
                                    <Tooltip
                                      content={({ active, payload }) => {
                                        if (active && payload && payload.length) {
                                          const isRent =
                                            selectedTransactionType === TransactionType.FOR_RENT;
                                          const isLastItem =
                                            payload[0].payload.price === (isRent ? '70+' : '9-10');
                                          const unit = isRent ? 'tr' : 'tỷ';
                                          return (
                                            <div className="bg-white p-2 border rounded shadow-sm">
                                              <p className="text-sm font-medium">
                                                {payload[0].payload.price}
                                                {unit}
                                                {isLastItem ? '+' : ''}: {payload[0].value} bất động
                                                sản
                                              </p>
                                            </div>
                                          );
                                        }
                                        return null;
                                      }}
                                    />
                                    <Area
                                      type="monotone"
                                      dataKey="count"
                                      stroke="#ef4444"
                                      strokeWidth={2}
                                      fill="url(#colorCount)"
                                    />
                                  </AreaChart>
                                </ResponsiveContainer>
                              </div>

                              {/* Price Slider */}
                              <div className="px-2">
                                <Slider
                                  min={0}
                                  max={actions.getCurrentPriceRanges().maxValue}
                                  step={actions.getCurrentPriceRanges().step}
                                  value={priceSliderValue}
                                  onValueChange={actions.setPriceSliderValue}
                                  className="relative flex w-full touch-none select-none items-center"
                                />
                                <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                                  <span>{actions.formatPriceLabel(priceSliderValue[0])}</span>
                                  <span>
                                    {actions.formatPriceLabel(
                                      priceSliderValue[1],
                                      priceSliderValue[1] ===
                                        actions.getCurrentPriceRanges().maxValue
                                    )}
                                  </span>
                                </div>
                              </div>

                              {/* Quick Price Ranges */}
                              <div className="grid grid-cols-2 gap-2">
                                {(selectedTransactionType === TransactionType.FOR_RENT
                                  ? quickPriceRangesRent
                                  : quickPriceRangesSale
                                ).map(range => (
                                  <Button
                                    key={range.label}
                                    variant="outline"
                                    className="text-sm h-10"
                                    onClick={() => {
                                      actions.setPriceSliderValue(range.value as [number, number]);
                                      actions.setMinPrice(range.value[0].toString());
                                      actions.setMaxPrice(range.value[1].toString());
                                    }}
                                  >
                                    {range.label}
                                  </Button>
                                ))}
                              </div>
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-4 ">
                            <h3 className="font-medium text-base">Diện tích</h3>
                            {/* Size Inputs */}
                            <div className="flex justify-between items-center gap-2">
                              <div className="flex flex-col w-1/2">
                                <label className="text-sm text-muted-foreground mb-1">
                                  Diện tích tối thiểu
                                </label>
                                <div className="relative">
                                  <Input
                                    value={minArea || ''}
                                    onChange={e =>
                                      actions.setMinArea(e.target.value.replace(/[^0-9]/g, ''))
                                    }
                                    type="text"
                                    placeholder="Từ"
                                    inputMode="numeric"
                                    className="pr-8"
                                  />
                                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                    m²
                                  </span>
                                </div>
                              </div>
                              <div className="flex flex-col w-1/2">
                                <label className="text-sm text-muted-foreground mb-1">
                                  Diện tích tối đa
                                </label>
                                <div className="relative">
                                  <Input
                                    value={maxArea || ''}
                                    onChange={e =>
                                      actions.setMaxArea(e.target.value.replace(/[^0-9]/g, ''))
                                    }
                                    type="text"
                                    placeholder="Đến"
                                    inputMode="numeric"
                                    className="pr-8"
                                  />
                                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                    m²
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Size Distribution Chart */}
                            <div className="h-[100px] w-full">
                              <ResponsiveContainer width="100%" height="100%">
                                <AreaChart
                                  data={sizeDistribution}
                                  margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                                >
                                  <defs>
                                    <linearGradient id="colorSizeCount" x1="0" y1="0" x2="0" y2="1">
                                      <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                      <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                                    </linearGradient>
                                  </defs>
                                  <XAxis
                                    dataKey="size"
                                    tickFormatter={value => `${value}m²`}
                                    tick={{ fontSize: 10 }}
                                    axisLine={false}
                                    tickLine={false}
                                  />
                                  <YAxis hide={true} domain={[0, 'dataMax']} />
                                  <Tooltip
                                    content={({ active, payload }) => {
                                      if (active && payload && payload.length) {
                                        const isLastItem = payload[0].payload.size === '1000+';
                                        return (
                                          <div className="bg-white p-2 border rounded shadow-sm">
                                            <p className="text-sm font-medium">
                                              {payload[0].payload.size}
                                              {isLastItem ? '+' : ''}: {payload[0].value} bất động
                                              sản
                                            </p>
                                          </div>
                                        );
                                      }
                                      return null;
                                    }}
                                  />
                                  <Area
                                    type="monotone"
                                    dataKey="count"
                                    stroke="#ef4444"
                                    strokeWidth={2}
                                    fill="url(#colorSizeCount)"
                                  />
                                </AreaChart>
                              </ResponsiveContainer>
                            </div>

                            {/* Size Slider */}
                            <div className="px-2">
                              <Slider
                                min={0}
                                max={1000}
                                step={5}
                                value={areaSliderValue}
                                onValueChange={actions.setAreaSliderValue}
                                className="relative flex w-full touch-none select-none items-center"
                              />
                              <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                                <span>{actions.formatSizeLabel(areaSliderValue[0])}</span>
                                <span>
                                  {actions.formatSizeLabel(
                                    areaSliderValue[1],
                                    areaSliderValue[1] === 1000
                                  )}
                                </span>
                              </div>
                            </div>

                            {/* Quick Size Ranges */}
                            <div className="grid grid-cols-2 gap-2">
                              {quickSizeRanges.map(range => (
                                <Button
                                  key={range.label}
                                  variant="outline"
                                  className="text-sm h-10"
                                  onClick={() => {
                                    actions.setAreaSliderValue(range.value as [number, number]);
                                    actions.setMinArea(range.value[0].toString());
                                    actions.setMaxArea(range.value[1].toString());
                                  }}
                                >
                                  {range.label}
                                </Button>
                              ))}
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-6">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium text-base">Số phòng ngủ</h4>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="exact-bed-match-sheet"
                                  checked={isExactBedMatch}
                                  onCheckedChange={() => actions.toggleExactBedMatch()}
                                />
                                <label
                                  htmlFor="exact-bed-match-sheet"
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  Chính xác số phòng
                                </label>
                              </div>
                            </div>
                            <RadioGroup.Root
                              value={selectedBedCount}
                              onValueChange={actions.setBedCount}
                              className="grid grid-cols-3 gap-2"
                            >
                              {(isExactBedMatch ? exactBedOptions : bedOptions).map(option => (
                                <RadioGroup.Item
                                  key={option.id}
                                  value={option.id}
                                  className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm">{option.label}</span>
                                </RadioGroup.Item>
                              ))}
                            </RadioGroup.Root>
                          </div>

                          <Separator />

                          <div className="space-y-4">
                            <h3 className="font-medium text-base">Số phòng tắm</h3>
                            <RadioGroup.Root
                              value={selectedBathCount}
                              onValueChange={actions.setBathCount}
                              className="grid grid-cols-3 gap-2"
                            >
                              {bathOptions.map(option => (
                                <RadioGroup.Item
                                  key={option.id}
                                  value={option.id}
                                  className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm">{option.label}</span>
                                </RadioGroup.Item>
                              ))}
                            </RadioGroup.Root>
                          </div>

                          <Separator />

                          <div className="space-y-4">
                            <h3 className="font-medium text-lg">Tiện ích</h3>
                            <div className="w-full grid grid-cols-2 md:grid-cols-3 gap-3">
                              {amenityOptions.map(option => (
                                <CheckboxPrimitive.Root
                                  key={option.id}
                                  onCheckedChange={() => actions.toggleAmenity(option.id)}
                                  checked={selectedAmenities.includes(option.id)}
                                  className="relative ring-[1px] ring-border rounded-lg p-3 text-start text-foreground data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm tracking-tight">
                                    {option.label}
                                  </span>
                                  <CheckboxPrimitive.Indicator className="absolute top-2 right-2">
                                    <CircleCheck className="fill-red-600 text-white" />
                                  </CheckboxPrimitive.Indicator>
                                </CheckboxPrimitive.Root>
                              ))}
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-4">
                            <h3 className="font-medium text-lg">Chi tiết bất động sản</h3>
                            <div className="w-full grid grid-cols-2 md:grid-cols-3 gap-3">
                              {detailOptions.map(option => (
                                <CheckboxPrimitive.Root
                                  key={option.id}
                                  onCheckedChange={() => actions.toggleDetail(option.id)}
                                  checked={selectedDetails.includes(option.id)}
                                  className="relative ring-[1px] ring-border rounded-lg p-3 text-start text-foreground data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm tracking-tight">
                                    {option.label}
                                  </span>
                                  <CheckboxPrimitive.Indicator className="absolute top-2 right-2">
                                    <CircleCheck className="fill-red-600 text-white" />
                                  </CheckboxPrimitive.Indicator>
                                </CheckboxPrimitive.Root>
                              ))}
                            </div>
                          </div>
                        </div>
                      </ScrollArea>
                      {/* Action Buttons for Desktop */}
                      <SheetFooter>
                        <div className="flex justify-between gap-2 pt-4">
                          <Button
                            variant="outline"
                            className="flex-1 text-red-600 hover:text-red-700"
                            onClick={handleClearFilters}
                          >
                            Đặt lại
                          </Button>
                          <Button
                            className="flex-1 bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                            onClick={() => handleApplyAndClose('moreFilters')}
                          >
                            Xem kết quả
                          </Button>
                        </div>
                      </SheetFooter>
                    </SheetContent>
                  </Sheet>
                )}

                {/* Clear Filters Button */}
                {countActiveFilters() > 0 && (
                  <Button
                    variant="outline"
                    onClick={handleClearFilters}
                    className="max-md:hidden w-full lg:w-auto text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <XIcon className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default memo(SearchFilter);
