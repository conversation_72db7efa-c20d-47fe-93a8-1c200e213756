# Search API Documentation

## Tổng quan

API Search cung cấp các chức năng để lưu trữ, quản lý và truy xuất các bộ lọc tìm kiếm của người dùng. <PERSON><PERSON> thống cho phép người dùng lưu các tiêu chí tìm kiếm phức tạp và sử dụng lại sau này.

## Base URL

```
/api/searches
```

## Authentication

Tất cả các API đều yêu cầu authentication với Bearer Token trong header:

```
Authorization: Bearer <your_jwt_token>
```

## API Endpoints

### 1. POST /api/searches

**Mô tả**: L<PERSON>u bộ lọc tìm kiếm của người dùng

**Method**: `POST`
**URL**: `/api/searches`
**Authentication**: Required
**Content-Type**: `application/json`

#### Request Parameters (Query String)

Tất cả parameters được truyền qua query string:

##### Basic Search Parameters

- `searchTerm` (string, optional): Từ khóa tìm kiếm
- `isDescending` (boolean, default: false): Sắp xếp giảm dần
- `sortBy` (enum, default: "name"): Tiêu chí sắp xếp
  - Values: `name`, `price`, `createdAt`, `updatedAt`, `distance`

##### Location Parameters

- `swLatitude` (double, optional): Latitude của điểm Southwest (range: -90 to 90)
- `swLongitude` (double, optional): Longitude của điểm Southwest (range: -180 to 180)
- `neLatitude` (double, optional): Latitude của điểm Northeast (range: -90 to 90)
- `neLongitude` (double, optional): Longitude của điểm Northeast (range: -180 to 180)

##### Property Type Filters

- `status` (array, optional): Trạng thái bất động sản
  - Values: `Available`, `Pending`, `Sold`, `Rented`
- `transactionType` (array, optional): Loại giao dịch
  - Values: `ForRent`, `ForSale`, `Project`
- `type` (array, optional): Loại bất động sản
  - Values: `Apartment`, `MiniServiceApartment`, `CommercialTownhouse`, `Motel`, `Airbnb`, `House`, `Townhouse`, `Villa`, `ShopHouse`, `LandPlot`, `ProjectLand`, `Office`, `Warehouse`, `Factory`, `Industrial`, `Hotel`, `SocialHousing`, `NewUrbanArea`, `EcoResort`, `Other`, `Project`

##### Property Detail Filters

- `propertyDetailFilters` (array, optional): Bộ lọc chi tiết
  - Values: `hasBasement`, `furnished`
- `amenityFilters` (array, optional): Tiện ích
  - Values: `Parking`, `Elevator`, `SwimmingPool`, `Gym`, `SecuritySystem`, `AirConditioning`, `Balcony`, `Garden`, `Playground`, `BackupGenerator`

##### Room Filters

- `bedrooms` (int, optional): Số phòng ngủ chính xác
- `minBedrooms` (int, optional): Số phòng ngủ tối thiểu
- `maxBedrooms` (int, optional): Số phòng ngủ tối đa
- `bathrooms` (int, optional): Số phòng tắm chính xác
- `minBathrooms` (int, optional): Số phòng tắm tối thiểu
- `maxBathrooms` (int, optional): Số phòng tắm tối đa
- `livingRooms` (int, optional): Số phòng khách chính xác
- `minLivingRooms` (int, optional): Số phòng khách tối thiểu
- `maxLivingRooms` (int, optional): Số phòng khách tối đa
- `kitchens` (int, optional): Số phòng bếp chính xác
- `minKitchens` (int, optional): Số phòng bếp tối thiểu
- `maxKitchens` (int, optional): Số phòng bếp tối đa

##### Area Filters

- `landArea` (double, optional): Diện tích đất
- `landWidth` (double, optional): Chiều rộng đất
- `landLength` (double, optional): Chiều dài đất
- `buildingArea` (double, optional): Diện tích xây dựng

##### Floor Filters

- `numberOfFloors` (int, optional): Số tầng
- `floorNumber` (int, optional): Tầng số
- `minFloorNumber` (int, optional): Tầng tối thiểu
- `maxFloorNumber` (int, optional): Tầng tối đa

##### Price Filters

- `minPrice` (double, optional): Giá tối thiểu
- `maxPrice` (double, optional): Giá tối đa

##### Orientation Filter

- `apartmentOrientation` (array, optional): Hướng căn hộ
  - Values: `North`, `South`, `East`, `West`, `NorthEast`, `NorthWest`, `SouthEast`, `SouthWest`

#### Example Request

```
POST /api/searches?searchTerm=apartment&status=Available&status=Pending&transactionType=ForRent&minPrice=1000000&maxPrice=5000000&bedrooms=2&amenityFilters=Parking&amenityFilters=Elevator&sortBy=price&isDescending=false
```

#### Response

**Success (201)**:

```json
{
  "code": 201,
  "status": true,
  "message": "Lưu filter thành công",
  "data": null
}
```

**Error (400)**:

```json
{
  "code": 400,
  "status": false,
  "message": "Dữ liệu query không hợp lệ",
  "data": {
    "errors": {
      "swLatitude": ["Southwest latitude phải trong khoảng -90 đến 90"]
    }
  }
}
```

### 2. GET /api/searches/saved

**Mô tả**: Lấy danh sách tất cả bộ lọc đã lưu của người dùng

**Method**: `GET`
**URL**: `/api/searches/saved`
**Authentication**: Required

#### Request Parameters (Query String)

- `pageNumber` (int, default: 1): Số trang (≥ 1)
- `pageSize` (int, default: 10): Kích thước trang (≥ 1)

#### Example Request

```
GET /api/searches/saved?pageNumber=1&pageSize=10
```

#### Response

**Success (200)**:

```json
{
  "code": 200,
  "status": true,
  "message": "Thành công",
  "data": {
    "searchCreateResponses": [
      {
        "id": "507f1f77bcf86cd799439011",
        "ownerSaving": {
          "id": "507f1f77bcf86cd799439012",
          "fullName": "Nguyễn Văn A",
          "email": "<EMAIL>",
          "avatar": "https://example.com/avatar.jpg",
          "phoneNumber": "0123456789",
          "gender": "Male"
        },
        "searchTerm": "apartment",
        "isDescending": false,
        "swLatitude": 10.762622,
        "neLatitude": 10.762622,
        "swLongitude": 106.660172,
        "neLongitude": 106.660172,
        "status": ["Available", "Pending"],
        "transactionType": ["ForRent"],
        "type": ["Apartment"],
        "propertyDetailFilters": ["furnished"],
        "amenityFilters": ["Parking", "Elevator"],
        "bedrooms": 2,
        "minBedrooms": null,
        "maxBedrooms": null,
        "bathrooms": 1,
        "minBathrooms": null,
        "maxBathrooms": null,
        "livingRooms": 1,
        "minLivingRooms": null,
        "maxLivingRooms": null,
        "kitchens": 1,
        "minKitchens": null,
        "maxKitchens": null,
        "landArea": null,
        "landWidth": null,
        "landLength": null,
        "buildingArea": 50.0,
        "numberOfFloors": null,
        "floorNumber": 5,
        "minPrice": 1000000.0,
        "maxPrice": 5000000.0,
        "apartmentOrientation": ["North", "East"],
        "sortBy": "price"
      }
    ],
    "page": 1,
    "limit": 10,
    "count": 1,
    "totalPages": 1
  }
}
```

**Error (400)**:

```json
{
  "code": 400,
  "status": false,
  "message": "Dữ liệu query không hợp lệ",
  "data": {
    "errors": {
      "pageNumber": ["Page number must be greater than or equal to 1."]
    }
  }
}
```

**Error (409)**:

```json
{
  "code": 409,
  "status": false,
  "message": "Conflict error message",
  "data": null
}
```

### 3. GET /api/searches/{id}/query-string

**Mô tả**: Lấy query string cho bộ lọc đã lưu theo ID

**Method**: `GET`
**URL**: `/api/searches/{id}/query-string`
**Authentication**: Required

#### Path Parameters

- `id` (string, required): ID của bộ lọc đã lưu (ObjectId format)

#### Example Request

```
GET /api/searches/507f1f77bcf86cd799439011/query-string
```

#### Response

**Success (200)**:

```json
{
  "code": 200,
  "status": true,
  "message": "Thành công",
  "data": "isDescending=false&searchTerm=apartment&swLatitude=10.762622&neLatitude=10.762622&swLongitude=106.660172&neLongitude=106.660172&status=Available&status=Pending&transactionType=ForRent&type=Apartment&propertyDetailFilters=furnished&amenityFilters=Parking&amenityFilters=Elevator&bedrooms=2&bathrooms=1&livingRooms=1&kitchens=1&buildingArea=50&floorNumber=5&minPrice=1000000&maxPrice=5000000&apartmentOrientation=North&apartmentOrientation=East&sortBy=price"
}
```

**Error (404)**:

```json
{
  "code": 404,
  "status": false,
  "message": "Not found saved-search",
  "data": null
}
```

**Error (400)**:

```json
{
  "code": 400,
  "status": false,
  "message": "Dữ liệu query không hợp lệ",
  "data": null
}
```

**Error (409)**:

```json
{
  "code": 409,
  "status": false,
  "message": "Conflict error message",
  "data": null
}
```

### 4. DELETE /api/searches/{id}/delete-saved-search

**Mô tả**: Xóa bộ lọc đã lưu theo ID

**Method**: `DELETE`
**URL**: `/api/searches/{id}/delete-saved-search`
**Authentication**: Required

#### Path Parameters

- `id` (string, required): ID của bộ lọc cần xóa (ObjectId format)

#### Example Request

```
DELETE /api/searches/507f1f77bcf86cd799439011/delete-saved-search
```

#### Response

**Success (200)**:

```json
{
  "code": 200,
  "status": true,
  "message": "Thành công",
  "data": null
}
```

**Error (404)**:

```json
{
  "code": 404,
  "status": false,
  "message": "Not found saved-search",
  "data": null
}
```

**Error (400)**:

```json
{
  "code": 400,
  "status": false,
  "message": "Dữ liệu query không hợp lệ",
  "data": null
}
```

**Error (409)**:

```json
{
  "code": 409,
  "status": false,
  "message": "Conflict error message",
  "data": null
}
```

## Error Handling

### Common Error Codes

- **400 Bad Request**: Dữ liệu đầu vào không hợp lệ
- **401 Unauthorized**: Chưa xác thực hoặc token không hợp lệ
- **404 Not Found**: Không tìm thấy tài nguyên
- **409 Conflict**: Xung đột dữ liệu

### Error Response Format

```json
{
  "code": <HTTP_STATUS_CODE>,
  "status": false,
  "message": "<ERROR_MESSAGE>",
  "data": <ERROR_DETAILS_OR_NULL>
}
```

## Data Models

### UserResponse

```json
{
  "id": "string",
  "fullName": "string",
  "email": "string",
  "avatar": "string|null",
  "phoneNumber": "string",
  "gender": "string"
}
```

### SearchCreateResponse

```json
{
  "id": "string",
  "ownerSaving": "UserResponse",
  "searchTerm": "string|null",
  "isDescending": "boolean",
  "swLatitude": "double|null",
  "neLatitude": "double|null",
  "swLongitude": "double|null",
  "neLongitude": "double|null",
  "status": "string[]",
  "transactionType": "string[]",
  "type": "string[]",
  "propertyDetailFilters": "string[]",
  "amenityFilters": "string[]",
  "bedrooms": "int|null",
  "minBedrooms": "int|null",
  "maxBedrooms": "int|null",
  "bathrooms": "int|null",
  "minBathrooms": "int|null",
  "maxBathrooms": "int|null",
  "livingRooms": "int|null",
  "minLivingRooms": "int|null",
  "maxLivingRooms": "int|null",
  "kitchens": "int|null",
  "minKitchens": "int|null",
  "maxKitchens": "int|null",
  "landArea": "double|null",
  "landWidth": "double|null",
  "landLength": "double|null",
  "buildingArea": "double|null",
  "numberOfFloors": "int|null",
  "floorNumber": "int|null",
  "minPrice": "double|null",
  "maxPrice": "double|null",
  "apartmentOrientation": "string[]",
  "sortBy": "string"
}
```

### SearchResponseWithPaging

```json
{
  "searchCreateResponses": "SearchCreateResponse[]",
  "page": "int",
  "limit": "int",
  "count": "int",
  "totalPages": "int"
}
```

## Usage Examples

### Frontend Integration

#### 1. Lưu bộ lọc tìm kiếm

```javascript
const saveSearchFilter = async filterParams => {
  const queryString = new URLSearchParams(filterParams).toString();

  const response = await fetch(`/api/searches?${queryString}`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  return await response.json();
};

// Example usage
const filterData = {
  searchTerm: 'apartment',
  status: ['Available', 'Pending'],
  transactionType: ['ForRent'],
  minPrice: 1000000,
  maxPrice: 5000000,
  bedrooms: 2,
  amenityFilters: ['Parking', 'Elevator'],
};

saveSearchFilter(filterData);
```

#### 2. Lấy danh sách bộ lọc đã lưu

```javascript
const getSavedSearches = async (page = 1, pageSize = 10) => {
  const response = await fetch(`/api/searches/saved?pageNumber=${page}&pageSize=${pageSize}`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return await response.json();
};
```

#### 3. Lấy query string từ bộ lọc đã lưu

```javascript
const getSearchQueryString = async searchId => {
  const response = await fetch(`/api/searches/${searchId}/query-string`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  const result = await response.json();
  return result.data; // Query string
};
```

#### 4. Xóa bộ lọc đã lưu

```javascript
const deleteSavedSearch = async searchId => {
  const response = await fetch(`/api/searches/${searchId}/delete-saved-search`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return await response.json();
};
```

## Notes

- Tất cả API đều yêu cầu authentication
- User chỉ có thể truy cập và quản lý các bộ lọc của chính mình
- Hệ thống sử dụng MongoDB với ObjectId format
- Pagination được hỗ trợ cho API lấy danh sách bộ lọc
- Query string được tự động tạo từ các tham số đã lưu
