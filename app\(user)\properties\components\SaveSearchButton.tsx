'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Bookmark, BookmarkCheck } from 'lucide-react';
import { useAuth } from '@/lib/providers/authProvider';
import { useSaveSearch } from '@/hooks/useSavedSearches';
import { SaveSearchParams } from '@/lib/api/services/fetchSavedSearches';
import { toast } from 'sonner';

interface SaveSearchButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
}

export default function SaveSearchButton({
  className,
  variant = 'outline',
  size = 'default',
}: SaveSearchButtonProps) {
  const { isAuthenticated } = useAuth();
  const searchParams = useSearchParams();
  const [isOpen, setIsOpen] = useState(false);
  const [searchName, setSearchName] = useState('');
  const saveSearchMutation = useSaveSearch();

  // Convert URL search params to SaveSearchParams
  const convertUrlParamsToSaveParams = (): SaveSearchParams => {
    const params: SaveSearchParams = {};

    // Basic search parameters
    const searchTerm = searchParams.get('searchTerm');
    if (searchTerm) params.searchTerm = searchTerm;

    const isDescending = searchParams.get('isDescending');
    if (isDescending) params.isDescending = isDescending === 'true';

    const sortBy = searchParams.get('sortBy');
    if (sortBy) params.sortBy = sortBy;

    // Location parameters
    const swLatitude = searchParams.get('swLatitude');
    if (swLatitude) params.swLatitude = parseFloat(swLatitude);

    const swLongitude = searchParams.get('swLongitude');
    if (swLongitude) params.swLongitude = parseFloat(swLongitude);

    const neLatitude = searchParams.get('neLatitude');
    if (neLatitude) params.neLatitude = parseFloat(neLatitude);

    const neLongitude = searchParams.get('neLongitude');
    if (neLongitude) params.neLongitude = parseFloat(neLongitude);

    // Array parameters
    const status = searchParams.getAll('status');
    if (status.length) params.status = status;

    const transactionType = searchParams.getAll('transactionType');
    if (transactionType.length) params.transactionType = transactionType;

    const type = searchParams.getAll('type');
    if (type.length) params.type = type;

    const propertyDetailFilters = searchParams.getAll('propertyDetailFilters');
    if (propertyDetailFilters.length) params.propertyDetailFilters = propertyDetailFilters;

    const amenityFilters = searchParams.getAll('amenityFilters');
    if (amenityFilters.length) params.amenityFilters = amenityFilters;

    const apartmentOrientation = searchParams.getAll('apartmentOrientation');
    if (apartmentOrientation.length) params.apartmentOrientation = apartmentOrientation;

    // Numeric parameters
    const bedrooms = searchParams.get('bedrooms');
    if (bedrooms) params.bedrooms = parseInt(bedrooms);

    const minBedrooms = searchParams.get('minBedrooms');
    if (minBedrooms) params.minBedrooms = parseInt(minBedrooms);

    const maxBedrooms = searchParams.get('maxBedrooms');
    if (maxBedrooms) params.maxBedrooms = parseInt(maxBedrooms);

    const bathrooms = searchParams.get('bathrooms');
    if (bathrooms) params.bathrooms = parseInt(bathrooms);

    const minBathrooms = searchParams.get('minBathrooms');
    if (minBathrooms) params.minBathrooms = parseInt(minBathrooms);

    const maxBathrooms = searchParams.get('maxBathrooms');
    if (maxBathrooms) params.maxBathrooms = parseInt(maxBathrooms);

    const livingRooms = searchParams.get('livingRooms');
    if (livingRooms) params.livingRooms = parseInt(livingRooms);

    const minLivingRooms = searchParams.get('minLivingRooms');
    if (minLivingRooms) params.minLivingRooms = parseInt(minLivingRooms);

    const maxLivingRooms = searchParams.get('maxLivingRooms');
    if (maxLivingRooms) params.maxLivingRooms = parseInt(maxLivingRooms);

    const kitchens = searchParams.get('kitchens');
    if (kitchens) params.kitchens = parseInt(kitchens);

    const minKitchens = searchParams.get('minKitchens');
    if (minKitchens) params.minKitchens = parseInt(minKitchens);

    const maxKitchens = searchParams.get('maxKitchens');
    if (maxKitchens) params.maxKitchens = parseInt(maxKitchens);

    // Area parameters
    const landArea = searchParams.get('landArea');
    if (landArea) params.landArea = parseFloat(landArea);

    const landWidth = searchParams.get('landWidth');
    if (landWidth) params.landWidth = parseFloat(landWidth);

    const landLength = searchParams.get('landLength');
    if (landLength) params.landLength = parseFloat(landLength);

    const buildingArea = searchParams.get('buildingArea');
    if (buildingArea) params.buildingArea = parseFloat(buildingArea);

    // Floor parameters
    const numberOfFloors = searchParams.get('numberOfFloors');
    if (numberOfFloors) params.numberOfFloors = parseInt(numberOfFloors);

    const floorNumber = searchParams.get('floorNumber');
    if (floorNumber) params.floorNumber = parseInt(floorNumber);

    // Price parameters
    const minPrice = searchParams.get('minPrice');
    if (minPrice) params.minPrice = parseFloat(minPrice);

    const maxPrice = searchParams.get('maxPrice');
    if (maxPrice) params.maxPrice = parseFloat(maxPrice);

    return params;
  };

  const handleSaveSearch = async () => {
    if (!isAuthenticated) {
      toast.error('Vui lòng đăng nhập để lưu tìm kiếm');
      return;
    }

    const searchParams = convertUrlParamsToSaveParams();

    // Add search term if provided by user
    if (searchName.trim()) {
      searchParams.searchTerm = searchName.trim();
    }

    try {
      await saveSearchMutation.mutateAsync(searchParams);
      setIsOpen(false);
      setSearchName('');
    } catch (error) {
      console.error('Error saving search:', error);
    }
  };

  const hasSearchParams = () => {
    return Array.from(searchParams.entries()).length > 0;
  };

  if (!isAuthenticated) {
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => toast.error('Vui lòng đăng nhập để lưu tìm kiếm')}
      >
        <Bookmark className="w-4 h-4 mr-2" />
        Lưu tìm kiếm
      </Button>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className} disabled={!hasSearchParams()}>
          <Bookmark className="w-4 h-4 mr-2" />
          Lưu tìm kiếm
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookmarkCheck className="w-5 h-5" />
            Lưu tìm kiếm
          </DialogTitle>
          <DialogDescription>
            Lưu bộ lọc tìm kiếm hiện tại để sử dụng lại sau này.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="search-name">Tên tìm kiếm (tùy chọn)</Label>
            <Input
              id="search-name"
              placeholder="VD: Căn hộ 2PN quận 1..."
              value={searchName}
              onChange={e => setSearchName(e.target.value)}
              maxLength={100}
            />
            <p className="text-xs text-muted-foreground">
              Nếu không nhập tên, hệ thống sẽ tự động tạo tên dựa trên bộ lọc
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
            Hủy
          </Button>
          <Button type="button" onClick={handleSaveSearch} disabled={saveSearchMutation.isPending}>
            {saveSearchMutation.isPending ? 'Đang lưu...' : 'Lưu tìm kiếm'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
