'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { APIProvider } from '@vis.gl/react-google-maps';

import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import PropertyListings from './PropertyListings';
import SearchFilter from './SearchFilter';
import PropertiesMap from './PropertiesMap';
import PropertyListingsHeader, { sortOptions, type SortOption } from './PropertyListingsHeader';
import { useProperties } from '@/hooks/useProperty';
import { useSearchStore } from '../store/useSearchStore';
import {
  PropertySearchParams,
  PropertyType,
  TransactionType,
  PropertyDetailFilters,
} from '@/lib/api/services/fetchProperty';
import { Skeleton } from '@/components/ui/skeleton';

// Add SearchFilterSkeleton component
function SearchFilterSkeleton() {
  return (
    <section className="w-full max-w-screen mx-auto bg-background text-foreground  ">
      <div className="ml-4">
        <div className="w-full border-0 bg-card overflow-hidden">
          <div className="p-2 space-y-2">
            <div className="flex flex-row gap-2">
              {/* Search Input Skeleton */}
              <div className="max-xl:hidden relative w-full md:w-[300px] flex-shrink-0">
                <Skeleton className="h-10 w-full" />
              </div>

              {/* Filter Buttons Skeletons */}
              <div className="flex-1 flex flex-wrap gap-2 max-md:hidden">
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
              </div>

              {/* Search and More Filters Buttons Skeletons */}
              <div className="col-span-2 sm:col-span-3 md:col-span-4 flex flex-row gap-2">
                <Skeleton className="h-10 w-[100px]" />
                <Skeleton className="max-md:hidden h-10 w-[40px]" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function PropertiesClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [searchFilters, setSearchFilters] = useState<PropertySearchParams>({});
  const [hoveredPropertyId, setHoveredPropertyId] = useState<string | null>(null);

  // Memoize hover handler to prevent PropertyListings re-renders
  const handlePropertyHover = useCallback((propertyId: string | null) => {
    setHoveredPropertyId(propertyId);
  }, []);
  const [panelWidth, setPanelWidth] = useState<number>(0);
  const [isMapView, setIsMapView] = useState<boolean>(true); // Default to grid view
  const panelRef = useRef<HTMLDivElement>(null);
  const itemsPerPage = 20;

  // Custom drawer state for mobile
  const [drawerHeight, setDrawerHeight] = useState<number>(40); // Initial height percentage
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStartY, setDragStartY] = useState<number>(0);
  const [dragStartHeight, setDragStartHeight] = useState<number>(40);
  const [windowWidth, setWindowWidth] = useState<number>(0);
  const drawerRef = useRef<HTMLDivElement>(null);

  // Header and bottom navigation height constants
  const HEADER_HEIGHT = 56; // h-14 = 56px
  // Add search store hook
  const setSearchTerm = useSearchStore(state => state.actions.setSearchTerm);

  const page = Number(searchParams.get('page')) || 1;

  // Updated sort logic - default to newest first
  const getCurrentSortOption = (): SortOption => {
    const sortBy = searchParams.get('sortBy') as 'price' | 'createdAt' | 'name' | 'none' | null;
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' | null;

    if (sortBy === 'none') {
      // User explicitly selected "Mặc định"
      return sortOptions.find(opt => opt.key === 'default')!;
    } else if (sortBy && sortOrder) {
      // User selected a specific sort option
      const option = sortOptions.find(opt => opt.sortBy === sortBy && opt.sortOrder === sortOrder);
      return option || sortOptions[0]; // fallback to newest first
    } else {
      // No sort params - default to newest first
      return sortOptions[0]; // This is now "Mới nhất"
    }
  };

  const currentSortOption = getCurrentSortOption();

  const initialCenter =
    searchParams.has('lat') && searchParams.has('lng')
      ? {
          lat: Number(searchParams.get('lat')),
          lng: Number(searchParams.get('lng')),
        }
      : undefined;

  const initialZoom = searchParams.has('zoom') ? Number(searchParams.get('zoom')) : 14;

  // Add effect to measure panel width
  useEffect(() => {
    const updatePanelWidth = () => {
      if (panelRef.current) {
        const width = panelRef.current.getBoundingClientRect().width;
        if (width > 0) {
          setPanelWidth(width);
        }
      }
    };

    // Initial measurement with a small delay
    const timer = setTimeout(updatePanelWidth, 100);

    // Set up resize observer
    const resizeObserver = new ResizeObserver(updatePanelWidth);
    if (panelRef.current) {
      resizeObserver.observe(panelRef.current);
    }

    return () => {
      clearTimeout(timer);
      resizeObserver.disconnect();
    };
  }, [isMapView]);

  // Convert URL search params to server-side API filters
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    const filters: PropertySearchParams = {
      pageNumber: page,
      pageSize: itemsPerPage,
    };

    const sortBy = params.get('sortBy');
    const sortOrder = params.get('sortOrder');

    // Apply sorting logic
    if (sortBy === 'none') {
      // User explicitly selected "Mặc định" - no sorting
      // Don't set sortBy or isDescending
    } else if (sortBy && sortOrder) {
      // User selected a specific sort option
      filters.sortBy = sortBy as 'price' | 'createdAt' | 'name';
      filters.isDescending = sortOrder === 'desc';
    } else {
      // No sort params - default to newest first
      filters.sortBy = 'createdAt';
      filters.isDescending = true;
    }

    // Location filters - prioritize in this order: map bounds > coordinates > city
    if (
      params.has('swLatitude') &&
      params.has('neLatitude') &&
      params.has('swLongitude') &&
      params.has('neLongitude')
    ) {
      // Use map bounds if available (user has moved the map or from search autocomplete)
      filters.swLatitude = Number(params.get('swLatitude'));
      filters.neLatitude = Number(params.get('neLatitude'));
      filters.swLongitude = Number(params.get('swLongitude'));
      filters.neLongitude = Number(params.get('neLongitude'));
    } else if (params.has('lat') && params.has('lng')) {
      // Use specific coordinates if available (from autocomplete selection)
      // Create a bounding box around the coordinates based on zoom level
      const lat = Number(params.get('lat'));
      const lng = Number(params.get('lng'));
      const zoom = Number(params.get('zoom')) || 14;

      // Calculate radius based on zoom level (higher zoom = smaller radius)
      const baseRadius = 0.1; // Base radius
      const radius = baseRadius / Math.pow(2, Math.max(0, zoom - 10));

      filters.swLatitude = lat - radius;
      filters.neLatitude = lat + radius;
      filters.swLongitude = lng - radius;
      filters.neLongitude = lng + radius;
    } else if (params.has('city')) {
      // Fallback to text-based location search
      if (!filters.searchTerm) {
        filters.searchTerm = params.get('city')!;
      }
    }

    // Extract search term
    if (params.has('searchTerm')) {
      filters.searchTerm = params.get('searchTerm') || undefined;
    }

    // Map property type
    if (params.has('type') || params.has('propertyType')) {
      const propertyType = params.get('type') || params.get('propertyType');

      // Check if the propertyType is a valid enum value
      if (propertyType && Object.values(PropertyType).includes(propertyType as PropertyType)) {
        filters.type = propertyType as PropertyType;
      }
    }

    // Map transaction type
    if (params.has('transactionType')) {
      const transactionType = params.get('transactionType');
      if (
        transactionType === TransactionType.FOR_SALE ||
        transactionType === TransactionType.FOR_RENT
      ) {
        filters.transactionType = transactionType;
      }
    }

    // Location filters
    if (params.has('city')) {
      // Note: City filtering will be handled differently in future
      // For now, we'll pass it as searchTerm if no other search term exists
      if (!filters.searchTerm && params.get('city')) {
        filters.searchTerm = params.get('city')!;
      }
    }

    // Price range filters
    if (params.has('minPrice')) {
      filters.minPrice = Number(params.get('minPrice')) || undefined;
    }
    if (params.has('maxPrice')) {
      filters.maxPrice = Number(params.get('maxPrice')) || undefined;
    }

    // Area filters
    if (params.has('minArea')) {
      filters.minLandArea = Number(params.get('minArea')) || undefined;
    }
    if (params.has('maxArea')) {
      filters.maxLandArea = Number(params.get('maxArea')) || undefined;
    }

    // Bedroom filters
    if (params.has('bedCount')) {
      const bedCount = params.get('bedCount');
      const isExactMatch = params.get('exactBedMatch') === 'true';

      if (bedCount && bedCount !== 'any') {
        if (bedCount === 'studio') {
          filters.bedrooms = 0;
        } else {
          const beds = parseInt(bedCount);
          if (isExactMatch) {
            filters.bedrooms = beds;
          } else {
            filters.minBedrooms = beds;
          }
        }
      }
    }

    // Bathroom filters
    if (params.has('bathCount')) {
      const bathCount = params.get('bathCount');
      if (bathCount && bathCount !== 'any') {
        const baths = parseInt(bathCount.replace('+', ''));
        filters.minBathrooms = baths;
      }
    }

    // Living Room and Kitchen filters
    if (params.has('livingRoomCount')) {
      const livingRoomCount = params.get('livingRoomCount');
      if (livingRoomCount && livingRoomCount !== 'any') {
        const livingRooms = parseInt(livingRoomCount.replace('+', ''));
        filters.minLivingRooms = livingRooms;
      }
    }
    if (params.has('kitchenCount')) {
      const kitchenCount = params.get('kitchenCount');
      if (kitchenCount && kitchenCount !== 'any') {
        const kitchens = parseInt(kitchenCount.replace('+', ''));
        filters.minKitchens = kitchens;
      }
    }

    // Amenity and Detail filters
    if (params.has('amenityFilters')) {
      filters.amenityFilters = params.get('amenityFilters')?.split(',') || undefined;
    }
    if (params.has('propertyDetailFilters')) {
      const details = params.get('propertyDetailFilters')?.split(',');
      if (details) {
        filters.propertyDetailFilters = details.map(
          d => d as PropertyDetailFilters
        ) as PropertyDetailFilters[];
      }
    }

    setSearchFilters(filters);
  }, [searchParams, page, currentSortOption]);

  // Update search store when URL parameters change
  useEffect(() => {
    const searchTerm = searchParams.get('searchTerm');
    if (searchTerm) {
      setSearchTerm(searchTerm);
    }
  }, [searchParams, setSearchTerm]);

  // Fetch properties with server-side filtering
  const { properties, isLoading, isError, error, isFetching, count, totalPages } =
    useProperties(searchFilters);

  // Use a ref to prevent re-renders during map interaction
  const isMapInteracting = useRef(false);

  const handleMapIdle = useCallback(
    (mapState: {
      bounds: google.maps.LatLngBounds;
      center: google.maps.LatLngLiteral;
      zoom: number;
    }) => {
      // Prevent rapid successive calls during map interaction
      if (isMapInteracting.current) return;
      isMapInteracting.current = true;
      const newParams = new URLSearchParams(searchParams.toString());
      const { bounds, center, zoom } = mapState;
      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();

      // Get current boundary values from URL
      const currentSwLat = searchParams.get('swLatitude');
      const currentNeLat = searchParams.get('neLatitude');
      const currentSwLng = searchParams.get('swLongitude');
      const currentNeLng = searchParams.get('neLongitude');
      const currentZoom = searchParams.get('zoom');

      // Check if boundaries have actually changed
      const newSwLat = sw.lat().toString();
      const newNeLat = ne.lat().toString();
      const newSwLng = sw.lng().toString();
      const newNeLng = ne.lng().toString();
      const newZoomStr = zoom.toString();

      const boundariesChanged =
        currentSwLat !== newSwLat ||
        currentNeLat !== newNeLat ||
        currentSwLng !== newSwLng ||
        currentNeLng !== newNeLng ||
        currentZoom !== newZoomStr;

      // Always update if boundaries changed (user interaction or initial load)
      if (boundariesChanged) {
        // Update all boundary parameters
        newParams.set('swLatitude', newSwLat);
        newParams.set('neLatitude', newNeLat);
        newParams.set('swLongitude', newSwLng);
        newParams.set('neLongitude', newNeLng);
        newParams.set('lat', center.lat.toString());
        newParams.set('lng', center.lng.toString());
        newParams.set('zoom', newZoomStr);

        // Remove city/district/ward when using map boundaries
        newParams.delete('city');
        newParams.delete('district');
        newParams.delete('ward');

        // Reset to first page when map changes
        newParams.set('page', '1');

        router.replace(`/properties?${newParams.toString()}`);
      }

      // Reset interaction flag after a brief delay
      setTimeout(() => {
        isMapInteracting.current = false;
      }, 100);
    },
    [router, searchParams]
  );

  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('page', newPage.toString());
    router.push(`/properties?${newParams.toString()}`);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getTitleText = () => {
    const city = searchParams.get('city') || 'Hồ Chí Minh';
    const transactionType = searchParams.get('transactionType')?.toLowerCase();

    if (
      transactionType === TransactionType.FOR_SALE ||
      transactionType === 'forsale' ||
      transactionType === 'for_sale'
    ) {
      return `Nhà bán ở ${city}`;
    } else if (
      transactionType === TransactionType.FOR_RENT ||
      transactionType === 'forrent' ||
      transactionType === 'for_rent'
    ) {
      return `Nhà cho thuê tại ${city}`;
    }
    return `Bất động sản ở ${city}`;
  };

  const handleSortChange = (option: SortOption) => {
    const newParams = new URLSearchParams(searchParams.toString());

    if (option.key === 'default') {
      // Set special value for no sorting
      newParams.set('sortBy', 'none');
      newParams.delete('sortOrder');
    } else {
      // Set the specific sort option
      newParams.set('sortBy', option.sortBy!);
      newParams.set('sortOrder', option.sortOrder!);
    }

    // Reset to first page when changing sort
    newParams.set('page', '1');
    router.push(`/properties?${newParams.toString()}`);
  };

  // Custom drawer handlers - Only for drag handle area
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      e.stopPropagation(); // Prevent event from bubbling
      setIsDragging(true);
      setDragStartY(e.touches[0].clientY);
      setDragStartHeight(drawerHeight);
    },
    [drawerHeight]
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!isDragging) return;
      e.stopPropagation(); // Prevent event from bubbling

      const currentY = e.touches[0].clientY;
      const deltaY = dragStartY - currentY; // Inverted: up is positive
      const availableHeight = window.innerHeight - HEADER_HEIGHT;

      const deltaPercent = (deltaY / availableHeight) * 100;

      const newHeight = Math.max(20, Math.min(85, dragStartHeight + deltaPercent)); // Max 85% to ensure clearance

      setDrawerHeight(newHeight);
    },
    [isDragging, dragStartY, dragStartHeight, HEADER_HEIGHT]
  );

  const handleTouchEnd = useCallback(
    (e: React.TouchEvent) => {
      if (!isDragging) return;
      e.stopPropagation(); // Prevent event from bubbling
      setIsDragging(false);

      // Snap to positions - adjusted for header clearance
      if (drawerHeight < 30) {
        setDrawerHeight(25); // Minimized
      } else if (drawerHeight < 60) {
        setDrawerHeight(40); // Half screen
      } else {
        setDrawerHeight(85); // Near full screen (below header)
      }
    },
    [isDragging, drawerHeight]
  );

  const handleMouseStart = useCallback(
    (e: React.MouseEvent) => {
      setIsDragging(true);
      setDragStartY(e.clientY);
      setDragStartHeight(drawerHeight);
    },
    [drawerHeight]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;

      const currentY = e.clientY;
      const deltaY = dragStartY - currentY; // Inverted: up is positive
      const availableHeight = window.innerHeight - HEADER_HEIGHT;

      const deltaPercent = (deltaY / availableHeight) * 100;

      const newHeight = Math.max(20, Math.min(85, dragStartHeight + deltaPercent)); // Max 85% to ensure clearance

      setDrawerHeight(newHeight);
    },
    [isDragging, dragStartY, dragStartHeight, HEADER_HEIGHT]
  );

  const handleMouseEnd = useCallback(() => {
    if (!isDragging) return;
    setIsDragging(false);

    // Snap to positions - adjusted for header clearance
    if (drawerHeight < 30) {
      setDrawerHeight(20); // Minimized
    } else if (drawerHeight < 60) {
      setDrawerHeight(40); // Half screen
    } else {
      setDrawerHeight(85); // Near full screen (below header)
    }
  }, [isDragging, drawerHeight]);

  // Add mouse event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseEnd);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseEnd);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseEnd]);

  // Update window width for mobile drawer
  useEffect(() => {
    const updateWindowWidth = () => {
      setWindowWidth(window.innerWidth);
    };

    updateWindowWidth();
    window.addEventListener('resize', updateWindowWidth);
    return () => window.removeEventListener('resize', updateWindowWidth);
  }, []);

  // Calculate pagination - Server already handles this
  const paginatedProperties = properties;

  // Memoize properties map component to prevent re-renders during drawer drag
  const memoizedPropertiesMap = useMemo(
    () => (
      <PropertiesMap
        properties={properties}
        onMapIdle={handleMapIdle}
        hoveredPropertyId={hoveredPropertyId}
        initialCenter={initialCenter}
        initialZoom={initialZoom}
      />
    ),
    [properties, handleMapIdle, hoveredPropertyId, initialCenter, initialZoom]
  );

  return (
    <div className=" h-[calc(100vh-10rem)] md:h-[calc(100vh-4rem)] flex flex-col">
      <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
        {/* Mobile Header */}
        {/* <div className="bg-background border-b flex-shrink-0 md:hidden">
          <div className="flex items-center px-2">
            {isLoading ? (
              <MobileHeaderButtonSkeleton />
            ) : (
              <Drawer>
                <DrawerTrigger asChild>
                  <Button
                    variant="outline"
                    className="rounded-full border-primary border-dashed shadow-none"
                  >
                    {getTitleText()}
                    <Badge variant="destructive" className="rounded-full px-1 py-px">
                      {count}+
                    </Badge>
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="h-[90vh]  ">
                  <DrawerHeader>
                    <DrawerTitle>{getTitleText()}</DrawerTitle>
                    <DrawerDescription>{count} bất động sản</DrawerDescription>
                  </DrawerHeader>

                  <div className="flex-1 overflow-y-auto p-4">
                    <PropertyListings
                      properties={paginatedProperties}
                      isLoading={isLoading}
                      isError={isError}
                      error={error}
                      isFetching={isFetching}
                      count={count}
                      totalPages={totalPages}
                      page={page}
                      onPageChange={handlePageChange}
                      onPropertyHover={handlePropertyHover}
                      containerWidth={panelWidth}
                    />
                  </div>
                </DrawerContent>
              </Drawer>
            )}

            <div className="flex-1 z-50">
              {isLoading ? <SearchFilterSkeleton /> : <SearchFilter />}
            </div>
          </div>
        </div> */}

        {/* Desktop Header */}
        <div className="bg-background border-b flex-shrink-0 hidden md:block z-50">
          {isLoading ? <SearchFilterSkeleton /> : <SearchFilter />}
        </div>

        {/* Mobile Layout - Full Screen Map */}
        <div className="md:hidden">
          <div className="fixed inset-0 z-30">{memoizedPropertiesMap}</div>
        </div>

        {/* Mobile Floating Drawer - Completely Separate */}
        <div
          ref={drawerRef}
          className="md:hidden fixed bottom-0 left-0 right-0 bg-background rounded-t-xl shadow-2xl transition-all duration-300 ease-out z-30 flex flex-col"
          style={{
            height: `calc(${drawerHeight}vh - ${HEADER_HEIGHT}px)`,
            maxHeight: `calc(100vh - ${HEADER_HEIGHT}px)`,

            transform: isDragging ? 'none' : undefined,
          }}
        >
          {/* Extended Drag Area - Larger touch target - ONLY for dragging */}
          <div
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            onMouseDown={handleMouseStart}
            style={{ touchAction: 'none' }} // Prevent scrolling ONLY in drag area
          >
            <div className="w-full flex justify-center pt-3 cursor-grab active:cursor-grabbing flex-shrink-0">
              {/* Visual Handle - Same size but centered in larger area */}
              <div className="w-12 h-1 bg-muted-foreground/30 rounded-full" />
            </div>

            {/* Drawer Header */}
            <div className="px-4 pb-0 border-b flex-shrink-0">
              <div className="flex items-center justify-center py-4">
                <h2 className="text-xs font-semibold">Hơn {count}+ bất động sản trong khu vực</h2>
              </div>
            </div>
          </div>
          {/* Drawer Content - Scrollable area with normal touch behavior */}
          <div
            className="flex-1 overflow-y-auto overscroll-contain px-4 py-4"
            style={{ touchAction: 'pan-y' }} // Allow vertical scrolling
          >
            <PropertyListings
              properties={paginatedProperties}
              isLoading={isLoading}
              isError={isError}
              error={error}
              isFetching={isFetching}
              count={count}
              totalPages={totalPages}
              page={page}
              onPageChange={handlePageChange}
              onPropertyHover={setHoveredPropertyId}
              containerWidth={windowWidth - 32} // Account for padding
            />
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="flex-1 min-h-0 hidden md:flex">
          {isMapView ? (
            // Map Split View
            <ResizablePanelGroup direction="horizontal" className="h-full">
              <ResizablePanel defaultSize={50} minSize={30}>
                <div className="h-full relative">{memoizedPropertiesMap}</div>
              </ResizablePanel>
              <ResizableHandle withHandle />
              <ResizablePanel defaultSize={50} minSize={30}>
                <div className="h-full flex flex-col" ref={panelRef}>
                  <PropertyListingsHeader
                    title={getTitleText()}
                    count={count}
                    isMapView={isMapView}
                    onViewToggle={setIsMapView}
                    currentSortOption={currentSortOption}
                    onSortChange={handleSortChange}
                  />
                  <div className="flex-1 overflow-y-auto p-4">
                    <PropertyListings
                      properties={paginatedProperties}
                      isLoading={isLoading}
                      isError={isError}
                      error={error}
                      isFetching={isFetching}
                      count={count}
                      totalPages={totalPages}
                      page={page}
                      onPageChange={handlePageChange}
                      onPropertyHover={handlePropertyHover}
                      containerWidth={panelWidth}
                    />
                  </div>
                </div>
              </ResizablePanel>
            </ResizablePanelGroup>
          ) : (
            // Grid View (Full Width)
            <div className="h-full w-full flex flex-col" ref={panelRef}>
              <PropertyListingsHeader
                title={getTitleText()}
                count={count}
                isMapView={isMapView}
                onViewToggle={setIsMapView}
                currentSortOption={currentSortOption}
                onSortChange={handleSortChange}
              />
              <div className="flex-1 overflow-y-auto p-4">
                <PropertyListings
                  properties={paginatedProperties}
                  isLoading={isLoading}
                  isError={isError}
                  error={error}
                  isFetching={isFetching}
                  count={count}
                  totalPages={totalPages}
                  page={page}
                  onPageChange={handlePageChange}
                  onPropertyHover={setHoveredPropertyId}
                  containerWidth={panelWidth}
                />
              </div>
            </div>
          )}
        </div>
      </APIProvider>
    </div>
  );
}
